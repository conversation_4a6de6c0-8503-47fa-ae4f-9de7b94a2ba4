import { test, describe, before, after } from 'node:test';
import assert from 'node:assert';
import { config, validateConfig } from './config.js';
import { getCustomFieldId, getIssue, updateIssue } from './utils/jira-api-client.js';
import { wait, logTestStep, logTestResult } from './utils/test-helpers.js';

describe('RequirementsUpdateHandler Integration Test', () => {
  let simpleReqFieldId = null;
  let fullReqFieldId = null;
  let originalSimpleValue = null;
  let originalFullValue = null;
  let testIssue = null;
  let projectId = null;
  let issueTypeId = null;

  before(async () => {
    console.log('Setting up integration test...');

    // Validate configuration
    validateConfig();

    // Get current issue state to extract context and restore later
    testIssue = await getIssue(config.TEST_ISSUE_ID);
    projectId = testIssue.fields.project.id;
    issueTypeId = testIssue.fields.issuetype.id;
    
    console.log(`Test Issue Project: ${testIssue.fields.project.key} (ID: ${projectId})`);
    console.log(`Test Issue Type: ${testIssue.fields.issuetype.name} (ID: ${issueTypeId})`);

    // Get field IDs using createmeta API with project and issue type context
    const [simpleReqId, fullReqId] = await getCustomFieldId(['Prompt', 'AI Requirements'], projectId, issueTypeId);
    simpleReqFieldId = simpleReqId;
    fullReqFieldId = fullReqId;
    
    console.log(`Prompt Field ID: ${simpleReqFieldId}`);
    console.log(`AI Requirements Field ID: ${fullReqFieldId}`);

    // Get current field values to restore later
    originalSimpleValue = testIssue.fields[simpleReqFieldId];
    originalFullValue = testIssue.fields[fullReqFieldId];
    
    console.log(`Original Prompt: ${originalSimpleValue}`);
    console.log(`Original AI Requirements: ${JSON.stringify(originalFullValue)}`);
  });

  after(async () => {
    console.log('Cleaning up integration test...');
    
    // Clear prompt field and restore AI Requirements field
    const restoreFields = {};
    // Always clear the prompt field to prevent triggering generation again
    restoreFields[simpleReqFieldId] = null;
    // Only restore AI Requirements if it had an original value
    if (originalFullValue !== null) {
      restoreFields[fullReqFieldId] = originalFullValue;
    }
    
    await updateIssue(config.TEST_ISSUE_ID, restoreFields);
    console.log('Prompt field cleared and AI Requirements field restored');
  });

  test('should update AI Requirements field when SimpleRequirements field is updated', async () => {
    logTestStep(0, 'Starting integration test');

    // Step 1: Clear the AI Requirements field and set SimpleRequirements field
    const testSimpleRequirements = 'Test requirement: Create only a short template for potential Requirements.';
    const updateFields = {
      [simpleReqFieldId]: testSimpleRequirements,
      [fullReqFieldId]: null // Clear the AI requirements field
    };

    logTestStep(1, 'Updating SimpleRequirements field and clearing AI Requirements field', {
      input: testSimpleRequirements,
      expected: 'AI Requirements field should be populated by the handler'
    });

    await updateIssue(config.TEST_ISSUE_ID, updateFields);

    // Verify the update was successful
    let issue = await getIssue(config.TEST_ISSUE_ID);
    assert.strictEqual(
      issue.fields[simpleReqFieldId], 
      testSimpleRequirements, 
      'SimpleRequirements field should be updated'
    );
    assert.strictEqual(
      issue.fields[fullReqFieldId], 
      null, 
      'AI Requirements field should be cleared'
    );
    
    logTestStep(2, 'Waiting for the handler to process the update', 'This may take up to 240 seconds...');

    // Poll for changes every 5 seconds for up to 240 seconds
    let attempts = 0;
    const maxAttempts = 48; // 48 * 5 = 240 seconds
    let updatedFullRequirements = null;
    let foundLoadingMessage = false;

    while (attempts < maxAttempts) {
      await wait(5);
      attempts++;

      console.log(`   Checking attempt ${attempts}/${maxAttempts}...`);
      issue = await getIssue(config.TEST_ISSUE_ID);
      updatedFullRequirements = issue.fields[fullReqFieldId];

      // Check for loading message first
      if (updatedFullRequirements !== null && !foundLoadingMessage) {
        const content = updatedFullRequirements?.content?.[0]?.content?.[0]?.text;
        if (content && content.includes('Requirements are being generated...')) {
          foundLoadingMessage = true;
          console.log(`   Found loading message after ${attempts * 5} seconds`);
          continue; // Keep waiting for actual content
        }
      }

      // Check for actual content (not loading message)
      if (updatedFullRequirements !== null && foundLoadingMessage) {
        const content = updatedFullRequirements?.content?.[0]?.content?.[0]?.text;
        if (content && !content.includes('Requirements are being generated...')) {
          logTestResult(true, `AI Requirements field updated with content after ${attempts * 5} seconds!`);
          break;
        }
      }

      // If we found content without loading message (direct update), that's also valid
      if (updatedFullRequirements !== null && !foundLoadingMessage) {
        const content = updatedFullRequirements?.content?.[0]?.content?.[0]?.text;
        if (content && !content.includes('Requirements are being generated...')) {
          logTestResult(true, `AI Requirements field updated directly after ${attempts * 5} seconds!`);
          break;
        }
      }
    }

    if (attempts >= maxAttempts) {
      const finalContent = updatedFullRequirements?.content?.[0]?.content?.[0]?.text;
      const status = foundLoadingMessage ?
        `Found loading message but no final content. Last content: ${finalContent}` :
        `No loading message or content found. Field value: ${JSON.stringify(updatedFullRequirements)}`;

      logTestResult(false, `AI Requirements field not properly updated after 240 seconds. ${status}`, {
        troubleshooting: [
          'The Forge app trigger is not activated by API updates',
          'The RequirementsUpdateHandler is not processing the event',
          'The OpenRouter API key is not configured correctly',
          'The handler is encountering an error',
          'The handler takes longer than 240 seconds to process'
        ],
        nextSteps: [
          'Check Forge app logs: `forge logs`',
          'Verify OpenRouter API key is set: `forge variables list`',
          'Try updating the field manually in Jira UI',
          'Check if the trigger works with UI updates vs API updates'
        ]
      });
      
      // Actually fail the test when timeout occurs
      assert.fail(`Integration test failed: AI Requirements field not properly updated after 240 seconds. ${status}`);
    }
    
    // Step 3: Final verification
    logTestStep(3, 'Final verification of AI Requirements field', JSON.stringify(updatedFullRequirements));

    // Verify that the AI Requirements field was updated with content
    assert.notStrictEqual(
      updatedFullRequirements,
      null,
      'AI Requirements field should not be null after processing'
    );

    // For ADF content, check if it has the expected structure
    if (updatedFullRequirements && typeof updatedFullRequirements === 'object') {

      // Check if there's actual content (not just empty structure)
      if (updatedFullRequirements.content && Array.isArray(updatedFullRequirements.content)) {
        assert.ok(
          updatedFullRequirements.content.length > 0,
          'AI Requirements field should contain non-empty content'
        );
      }
    } else if (typeof updatedFullRequirements === 'string') {
      assert.ok(
        updatedFullRequirements.trim().length > 0,
        'AI Requirements field should contain non-empty string content'
      );
    }

    logTestResult(true, 'Integration test completed successfully!');
  });
});
