import { jest } from '@jest/globals';

// Mock the Forge kvs module since it won't be available in test environment
jest.unstable_mockModule('@forge/kvs', () => ({
  kvs: {
    get: jest.fn(),
    set: jest.fn(),
    delete: jest.fn(),
    query: jest.fn(() => ({
      where: jest.fn(() => ({
        getMany: jest.fn()
      }))
    }))
  },
  WhereConditions: {
    beginsWith: jest.fn((prefix) => ({ type: 'beginsWith', value: prefix }))
  }
}));

// Import after mocking
const { SettingsService } = await import('../../../src/services/SettingsService.js');
const { kvs, WhereConditions } = await import('@forge/kvs');

describe('SettingsService', () => {
  let service;
  const mockProjectId = '12345';

  beforeEach(() => {
    service = new SettingsService();
    jest.clearAllMocks();
  });

  describe('getDefaultTemplateForIssueType', () => {
    it('should return a non-empty template for story', () => {
      const template = service.getDefaultTemplateForIssueType('story');
      
      expect(template).toBeTruthy();
      expect(typeof template).toBe('string');
      expect(template.length).toBeGreaterThan(0);
    });

    it('should return a non-empty template for task', () => {
      const template = service.getDefaultTemplateForIssueType('task');
      
      expect(template).toBeTruthy();
      expect(typeof template).toBe('string');
      expect(template.length).toBeGreaterThan(0);
    });

    it('should return a non-empty template for bug', () => {
      const template = service.getDefaultTemplateForIssueType('bug');
      
      expect(template).toBeTruthy();
      expect(typeof template).toBe('string');
      expect(template.length).toBeGreaterThan(0);
    });

    it('should return empty template for unknown issue types', () => {
      const template = service.getDefaultTemplateForIssueType('unknown-type');
      expect(template).toBe('');
    });
  });

  describe('getTemplateForIssueType', () => {
    it('should return stored template when available', async () => {
      const storedTemplate = 'Custom template content';
      kvs.get.mockResolvedValue(storedTemplate);

      const result = await service.getTemplateForIssueType('story', mockProjectId);

      expect(kvs.get).toHaveBeenCalledWith('project-12345-ai-template-story');
      expect(result).toBe(storedTemplate);
    });

    it('should return default template when storage returns null', async () => {
      kvs.get.mockResolvedValue(null);

      const result = await service.getTemplateForIssueType('story', mockProjectId);

      expect(kvs.get).toHaveBeenCalledWith('project-12345-ai-template-story');
      expect(result).toBeTruthy();
      expect(typeof result).toBe('string');
      expect(result.length).toBeGreaterThan(0);
    });

    it('should handle issue types with spaces', async () => {
      kvs.get.mockResolvedValue(null);

      const result = await service.getTemplateForIssueType('Sub Task', mockProjectId);

      expect(kvs.get).toHaveBeenCalledWith('project-12345-ai-template-sub-task');
      expect(result).toBe(''); // Unknown type returns empty template
    });

    it('should handle case insensitive issue types', async () => {
      kvs.get.mockResolvedValue(null);

      const result = await service.getTemplateForIssueType('STORY', mockProjectId);

      expect(kvs.get).toHaveBeenCalledWith('project-12345-ai-template-story');
      expect(result).toBeTruthy();
      expect(typeof result).toBe('string');
      expect(result.length).toBeGreaterThan(0);
    });

    it('should return null for invalid issue type', async () => {
      const result1 = await service.getTemplateForIssueType(null, mockProjectId);
      const result2 = await service.getTemplateForIssueType('', mockProjectId);
      
      expect(result1).toBeNull();
      expect(result2).toBeNull();
    });
  });

  describe('saveTemplateForIssueType', () => {
    it('should save template with correct storage key', async () => {
      kvs.set.mockResolvedValue(undefined);

      const result = await service.saveTemplateForIssueType('story', 'Custom template', mockProjectId);

      expect(result).toBe(true);
      expect(kvs.set).toHaveBeenCalledWith('project-12345-ai-template-story', 'Custom template');
    });

    it('should normalize issue type name', async () => {
      kvs.set.mockResolvedValue(undefined);

      const result = await service.saveTemplateForIssueType('Sub Task', 'Custom template', mockProjectId);

      expect(result).toBe(true);
      expect(kvs.set).toHaveBeenCalledWith('project-12345-ai-template-sub-task', 'Custom template');
    });

    it('should return false when storage throws error', async () => {
      kvs.set.mockRejectedValue(new Error('Storage error'));

      const result = await service.saveTemplateForIssueType('story', 'Custom template', mockProjectId);

      expect(result).toBe(false);
    });
  });

  describe('getTemplatesForIssueTypes', () => {
    it('should return templates for multiple issue types', async () => {
      kvs.get
        .mockResolvedValueOnce(null) // story - will use default
        .mockResolvedValueOnce('Custom task template'); // task - will use stored

      const result = await service.getTemplatesForIssueTypes(['story', 'task'], mockProjectId);

      expect(result).toEqual({
        story: expect.any(String),
        task: 'Custom task template'
      });
      expect(result.story.length).toBeGreaterThan(0);
    });
  });

  describe('getAIConfig', () => {
    it('should return stored config when available', async () => {
      const mockConfig = {
        model: 'test-model',
        temperature: 0.8,
        maxTokens: 2000
      };

      kvs.get
        .mockResolvedValueOnce(mockConfig.model)
        .mockResolvedValueOnce(mockConfig.temperature)
        .mockResolvedValueOnce(mockConfig.maxTokens);

      const result = await service.getAIConfig(mockProjectId);

      expect(result).toEqual(mockConfig);
    });

    it('should return default config when storage throws error', async () => {
      kvs.get.mockRejectedValue(new Error('Storage error'));

      const result = await service.getAIConfig(mockProjectId);

      expect(result).toEqual({
        model: expect.any(String),
        temperature: expect.any(Number),
        maxTokens: expect.any(Number)
      });
    });
  });

  describe('saveSettings', () => {
    it('should save all AI settings successfully', async () => {
      const settings = {
        aiModel: 'custom-model',
        temperature: '0.9',
        maxTokens: '1500'
      };

      kvs.set.mockResolvedValue(undefined);

      const result = await service.saveSettings(settings, mockProjectId);

      expect(result).toBe(true);
      expect(kvs.set).toHaveBeenCalledTimes(3);
      expect(kvs.set).toHaveBeenCalledWith('project-12345-ai-settings-model', settings.aiModel);
      expect(kvs.set).toHaveBeenCalledWith('project-12345-ai-settings-temperature', 0.9);
      expect(kvs.set).toHaveBeenCalledWith('project-12345-ai-settings-max-tokens', 1500);
    });

    it('should return false when storage throws error', async () => {
      const settings = { aiModel: 'custom-model' };
      kvs.set.mockRejectedValue(new Error('Storage error'));

      const result = await service.saveSettings(settings, mockProjectId);

      expect(result).toBe(false);
    });
  });

  describe('resetToDefaults', () => {
    it('should delete all stored AI settings and return defaults', async () => {
      kvs.delete.mockResolvedValue(undefined);

      const result = await service.resetToDefaults(mockProjectId);

      expect(kvs.delete).toHaveBeenCalledTimes(3);
      expect(kvs.delete).toHaveBeenCalledWith('project-12345-ai-settings-model');
      expect(kvs.delete).toHaveBeenCalledWith('project-12345-ai-settings-temperature');
      expect(kvs.delete).toHaveBeenCalledWith('project-12345-ai-settings-max-tokens');
      expect(result).toEqual(service.getDefaultSettings());
    });
  });

  describe('resetTemplatesToDefaults', () => {
    it('should delete all template keys', async () => {
      const mockTemplateResults = {
        results: [
          { key: 'project-12345-ai-template-story', value: 'Story template' },
          { key: 'project-12345-ai-template-task', value: 'Task template' },
          { key: 'project-12345-ai-template-bug', value: 'Bug template' }
        ]
      };

      const mockQuery = {
        where: jest.fn(() => ({
          getMany: jest.fn().mockResolvedValue(mockTemplateResults)
        }))
      };
      kvs.query.mockReturnValue(mockQuery);
      kvs.delete.mockResolvedValue(undefined);

      const result = await service.resetTemplatesToDefaults(mockProjectId);

      expect(kvs.query).toHaveBeenCalled();
      expect(mockQuery.where).toHaveBeenCalledWith('key', WhereConditions.beginsWith('project-12345-ai-template-'));
      expect(kvs.delete).toHaveBeenCalledTimes(3);
      expect(kvs.delete).toHaveBeenCalledWith('project-12345-ai-template-story');
      expect(kvs.delete).toHaveBeenCalledWith('project-12345-ai-template-task');
      expect(kvs.delete).toHaveBeenCalledWith('project-12345-ai-template-bug');
      expect(result).toEqual({});
    });
  });

  describe('normalizeIssueType', () => {
    it('should convert to lowercase and replace spaces with hyphens', () => {
      expect(service.normalizeIssueType('Story')).toBe('story');
      expect(service.normalizeIssueType('Sub Task')).toBe('sub-task');
      expect(service.normalizeIssueType('EPIC')).toBe('epic');
    });

    it('should return null for invalid input', () => {
      expect(service.normalizeIssueType(null)).toBe(null);
      expect(service.normalizeIssueType('')).toBe(null);
    });
  });

  describe('getTemplateStorageKey', () => {
    it('should generate correct storage key', () => {
      expect(service.getTemplateStorageKey('story', mockProjectId)).toBe('project-12345-ai-template-story');
      expect(service.getTemplateStorageKey('sub-task', mockProjectId)).toBe('project-12345-ai-template-sub-task');
    });
  });

  describe('getAllTemplates', () => {
    it('should return all stored templates', async () => {
      const mockTemplateResults = {
        results: [
          { key: 'project-12345-ai-template-story', value: 'Story template' },
          { key: 'project-12345-ai-template-task', value: 'Task template' }
        ]
      };

      const mockQuery = {
        where: jest.fn(() => ({
          getMany: jest.fn().mockResolvedValue(mockTemplateResults)
        }))
      };
      kvs.query.mockReturnValue(mockQuery);

      const result = await service.getAllTemplates(mockProjectId);

      expect(kvs.query).toHaveBeenCalled();
      expect(mockQuery.where).toHaveBeenCalledWith('key', WhereConditions.beginsWith('project-12345-ai-template-'));
      expect(result).toEqual({
        story: 'Story template',
        task: 'Task template'
      });
    });

    it('should handle query results with no templates', async () => {
      const mockTemplateResults = {
        results: []
      };

      const mockQuery = {
        where: jest.fn(() => ({
          getMany: jest.fn().mockResolvedValue(mockTemplateResults)
        }))
      };
      kvs.query.mockReturnValue(mockQuery);

      const result = await service.getAllTemplates(mockProjectId);

      expect(result).toEqual({});
    });

    it('should handle query errors', async () => {
      const mockQuery = {
        where: jest.fn(() => ({
          getMany: jest.fn().mockRejectedValue(new Error('Query failed'))
        }))
      };
      kvs.query.mockReturnValue(mockQuery);

      const result = await service.getAllTemplates(mockProjectId);

      expect(result).toEqual({});
    });
  });

  describe('deleteTemplateForIssueType', () => {
    it('should delete template for specific issue type', async () => {
      kvs.delete.mockResolvedValue(undefined);

      const result = await service.deleteTemplateForIssueType('story', mockProjectId);

      expect(result).toBe(true);
      expect(kvs.delete).toHaveBeenCalledWith('project-12345-ai-template-story');
    });

    it('should handle delete errors', async () => {
      kvs.delete.mockRejectedValue(new Error('Delete failed'));

      const result = await service.deleteTemplateForIssueType('story', mockProjectId);

      expect(result).toBe(false);
    });

    it('should return false for invalid issue type', async () => {
      const result = await service.deleteTemplateForIssueType(null, mockProjectId);

      expect(result).toBe(false);
      expect(kvs.delete).not.toHaveBeenCalled();
    });
  });

  describe('getSettings', () => {
    it('should return stored settings when available', async () => {
      kvs.get
        .mockResolvedValueOnce('custom-model')
        .mockResolvedValueOnce(0.8)
        .mockResolvedValueOnce(2000);

      const result = await service.getSettings(mockProjectId);

      expect(result).toEqual({
        aiModel: 'custom-model',
        temperature: 0.8,
        maxTokens: 2000
      });
    });

    it('should return default settings when storage returns null values', async () => {
      kvs.get
        .mockResolvedValueOnce(null)
        .mockResolvedValueOnce(undefined)
        .mockResolvedValueOnce(null);

      const result = await service.getSettings(mockProjectId);

      expect(result.aiModel).toBeDefined();
      expect(result.temperature).toBeDefined();
      expect(result.maxTokens).toBeDefined();
    });

    it('should return default settings when storage throws error', async () => {
      kvs.get.mockRejectedValue(new Error('Storage error'));

      const result = await service.getSettings(mockProjectId);

      expect(result).toEqual(service.getDefaultSettings());
    });
  });
});