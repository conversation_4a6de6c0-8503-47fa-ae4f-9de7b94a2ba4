/**
 * Unit tests for ConfluenceService
 */

import { jest } from '@jest/globals';

// Mock confluence-helpers
jest.unstable_mockModule('../../../src/utils/confluence-helpers.js', () => ({
  getSpaces: jest.fn(),
  getAllPagesFromSpace: jest.fn(),
  getPageContent: jest.fn(),
  getPagesByIds: jest.fn(),
  getAllPagesFromSpaces: jest.fn()
}));

// Mock KVS
jest.unstable_mockModule('@forge/kvs', () => ({
  kvs: {
    get: jest.fn(),
    set: jest.fn(),
    delete: jest.fn()
  }
}));

// Mock adf-to-md
const mockConvert = jest.fn().mockReturnValue({ result: 'converted markdown' });
jest.unstable_mockModule('adf-to-md', () => ({
  convert: mockConvert
}));

// Mock AIService for circular dependency in getProjectContext
jest.unstable_mockModule('../../../src/services/AIService.js', () => ({
  AIService: jest.fn().mockImplementation(() => ({
    analyzePageRelevancy: jest.fn().mockResolvedValue(['123', '456']),
    generatePageSummary: jest.fn().mockResolvedValue('Mock AI-generated summary')
  }))
}));

// Mock CachingService
jest.unstable_mockModule('../../../src/services/CachingService.js', () => ({
  CachingService: jest.fn().mockImplementation(() => ({
    getCachedPageContent: jest.fn().mockResolvedValue(null),
    setCachedPageContent: jest.fn().mockResolvedValue(true),
    getCachedPageSummary: jest.fn().mockResolvedValue(null),
    setCachedPageSummary: jest.fn().mockResolvedValue(true),
    getCachedPageContents: jest.fn().mockResolvedValue({ cached: [], uncachedIds: [] }),
    invalidatePageCache: jest.fn().mockResolvedValue(true)
  }))
}));

// Import after mocking
const { ConfluenceService } = await import('../../../src/services/ConfluenceService.js');
const {
  getSpaces,
  getPageContent,
  getAllPagesFromSpace,
  getAllPagesFromSpaces
} = await import('../../../src/utils/confluence-helpers.js');
const { kvs } = await import('@forge/kvs');

describe('ConfluenceService', () => {
  let confluenceService;
  let mockCachingService;
  let mockAIService;

  beforeEach(() => {
    // Create mocks for injected services
    mockCachingService = {
      getCachedPageContent: jest.fn().mockResolvedValue(null),
      setCachedPageContent: jest.fn().mockResolvedValue(true),
      getCachedPageSummary: jest.fn().mockResolvedValue(null),
      setCachedPageSummary: jest.fn().mockResolvedValue(true),
      getCachedPageContents: jest.fn().mockResolvedValue({ cached: [], uncachedIds: [] }),
      invalidatePageCache: jest.fn().mockResolvedValue(true)
    };

    mockAIService = {
      analyzePageRelevancy: jest.fn().mockResolvedValue(['123', '456']),
      generatePageSummary: jest.fn().mockResolvedValue('Mock AI-generated summary')
    };

    confluenceService = new ConfluenceService();
    
    // Replace the injected services with our mocks
    confluenceService.cachingService = mockCachingService;
    confluenceService.aiService = mockAIService;

    // Reset all mocks
    jest.clearAllMocks();

    // Re-setup adf-to-md mock after clearing
    mockConvert.mockReturnValue({ result: 'converted markdown' });

    kvs.get.mockResolvedValue(null);
    kvs.set.mockResolvedValue(undefined);
    kvs.delete.mockResolvedValue(undefined);
  });


  describe('getSpaces', () => {
    it('should call helper function and return result', async () => {
      const mockSpaces = [
        { key: 'SPACE1', name: 'Space 1' },
        { key: 'SPACE2', name: 'Space 2' }
      ];

      getSpaces.mockResolvedValue(mockSpaces);

      const result = await confluenceService.getSpaces();

      expect(getSpaces).toHaveBeenCalledWith();
      expect(result).toEqual(mockSpaces);
    });
  });



  describe('extractPlainTextContent', () => {
    it('should extract plain text from ADF content', () => {
      const pageData = {
        body: {
          atlas_doc_format: {
            value: '{"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "Test content"}]}]}'
          }
        }
      };

      const result = confluenceService.extractPlainTextContent(pageData);

      expect(result).toBe('converted markdown');
    });

    it('should handle missing body data', () => {
      const result = confluenceService.extractPlainTextContent({});
      expect(result).toBe('');
    });

    it('should handle null page data', () => {
      const result = confluenceService.extractPlainTextContent(null);
      expect(result).toBe('');
    });

    it('should handle ADF content parsing', () => {
      const pageData = {
        body: {
          atlas_doc_format: {
            value: {"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "Test content"}]}]}
          }
        }
      };

      const result = confluenceService.extractPlainTextContent(pageData);
      expect(result).toBe('converted markdown');
    });
  });



  describe('getPageSummaryWithCache', () => {
    const pageId = 'page-123';
    const projectId = 'proj-456';
    const mockPageData = {
      id: pageId,
      title: 'Test Page',
      body: {
        atlas_doc_format: {
          value: '{"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "Test content"}]}]}'
        }
      },
      version: { number: 5 }
    };

    it('should return cached summary when available', async () => {
      const cachedSummary = 'Cached summary content';
      mockCachingService.getCachedPageSummary.mockResolvedValue(cachedSummary);

      const result = await confluenceService.getPageSummaryWithCache(mockPageData, projectId);

      expect(mockCachingService.getCachedPageSummary).toHaveBeenCalledWith(pageId, projectId, 5);
      expect(result).toBe(cachedSummary);
      expect(mockAIService.generatePageSummary).not.toHaveBeenCalled();
    });

    it('should generate new summary when cache is empty', async () => {
      const generatedSummary = 'AI-generated summary';
      mockCachingService.getCachedPageSummary.mockResolvedValue(null);
      mockAIService.generatePageSummary.mockResolvedValue(generatedSummary);

      const result = await confluenceService.getPageSummaryWithCache(mockPageData, projectId);

      expect(mockCachingService.getCachedPageSummary).toHaveBeenCalledWith(pageId, projectId, 5);
      expect(mockAIService.generatePageSummary).toHaveBeenCalledWith('Test Page', 'converted markdown', projectId);
      expect(mockCachingService.setCachedPageSummary).toHaveBeenCalledWith(pageId, projectId, generatedSummary, 5);
      expect(result).toBe(generatedSummary);
    });

    it('should return null when page is not found', async () => {
      const pageDataWithoutId = { ...mockPageData, pageId: undefined, id: undefined };

      const result = await confluenceService.getPageSummaryWithCache(pageDataWithoutId, projectId);

      expect(result).toBeNull();
    });

    it('should return empty string when page has no content', async () => {
      const emptyPageData = {
        id: pageId,
        pageId: pageId,
        title: 'Test Page',
        body: {
          atlas_doc_format: {
            value: '{"type": "doc", "content": []}'  // Empty content but valid ADF
          }
        },
        version: { number: 5 }
      };
      mockCachingService.getCachedPageSummary.mockResolvedValue(null);
      // Mock empty content extraction
      mockConvert.mockReturnValueOnce({ result: '' });

      const result = await confluenceService.getPageSummaryWithCache(emptyPageData, projectId);

      expect(result).toBe('');
      expect(mockAIService.generatePageSummary).not.toHaveBeenCalled();
      expect(mockCachingService.setCachedPageSummary).toHaveBeenCalledWith(pageId, projectId, '', 5);
    });

    it('should handle AI service failures gracefully', async () => {
      mockCachingService.getCachedPageSummary.mockResolvedValue(null);
      mockAIService.generatePageSummary.mockResolvedValue(null);
      // Temporarily override the mock to return content so AI gets called
      mockConvert.mockReturnValueOnce({ result: 'some content' });

      const result = await confluenceService.getPageSummaryWithCache(mockPageData, projectId);

      expect(result).toBeNull();
      expect(mockCachingService.setCachedPageSummary).not.toHaveBeenCalled();
    });
  });

  describe('getPageSummariesBatch', () => {
    const pageObjects = [
      { pageId: 'page-1', id: 'page-1', title: 'Page 1' },
      { pageId: 'page-2', id: 'page-2', title: 'Page 2' },
      { pageId: 'page-3', id: 'page-3', title: 'Page 3' }
    ];
    const projectId = 'proj-123';

    it('should process multiple pages and return summaries', async () => {
      // Mock different responses for each page
      confluenceService.getPageSummaryWithCache = jest.fn()
        .mockResolvedValueOnce('Summary for page 1')
        .mockResolvedValueOnce('Summary for page 2')
        .mockResolvedValueOnce(null); // page-3 has no summary

      const result = await confluenceService.getPageSummariesBatch(pageObjects, projectId);

      expect(confluenceService.getPageSummaryWithCache).toHaveBeenCalledTimes(3);
      expect(confluenceService.getPageSummaryWithCache).toHaveBeenCalledWith(pageObjects[0], projectId);
      expect(confluenceService.getPageSummaryWithCache).toHaveBeenCalledWith(pageObjects[1], projectId);
      expect(confluenceService.getPageSummaryWithCache).toHaveBeenCalledWith(pageObjects[2], projectId);

      expect(result).toEqual({
        'page-1': 'Summary for page 1',
        'page-2': 'Summary for page 2'
        // page-3 should not be included since it returned null
      });
    });

    it('should return empty object for empty page list', async () => {
      const result = await confluenceService.getPageSummariesBatch([], projectId);

      expect(result).toEqual({});
    });

    it('should handle errors gracefully', async () => {
      confluenceService.getPageSummaryWithCache = jest.fn()
        .mockRejectedValue(new Error('Summary generation failed'));

      const result = await confluenceService.getPageSummariesBatch(pageObjects, projectId);

      expect(result).toEqual({});
    });
  });

});