/**
 * Unit tests for LicenseService
 */

import { jest } from '@jest/globals';
import { LicenseService } from '../../../src/services/LicenseService.js';

// Mock dependencies
jest.mock('@forge/api', () => ({
  requestJira: jest.fn()
}));

jest.mock('@forge/kvs', () => ({
  kvs: {
    get: jest.fn(),
    set: jest.fn(),
    delete: jest.fn()
  }
}));

jest.mock('../../../src/utils/license-errors.js', () => ({
  createLicenseBlock: jest.fn((reason, message, upgradeUrl) => ({
    allowed: false,
    reason,
    message: message || `Default message for ${reason}`,
    upgradeUrl
  })),
  createLicenseAllow: jest.fn((message) => ({
    allowed: true,
    reason: 'licensed',
    message: message || 'Operation allowed'
  })),
  logLicenseEnforcement: jest.fn()
}));

import { requestJira } from '@forge/api';
import { kvs } from '@forge/kvs';
import { createLicenseBlock, createLicenseAllow, logLicenseEnforcement } from '../../../src/utils/license-errors.js';

describe('LicenseService', () => {
  let service;
  const mockCloudId = 'test-cloud-id';

  beforeEach(() => {
    service = new LicenseService();
    jest.clearAllMocks();
  });

  describe('validateLicense', () => {
    it('should return cached license status when available', async () => {
      const cachedStatus = {
        isValid: true,
        status: 'active',
        tier: 'premium',
        checkedAt: Date.now() - 60000 // 1 minute ago
      };

      kvs.get.mockResolvedValue(cachedStatus);

      const result = await service.validateLicense(mockCloudId);

      expect(result).toEqual(cachedStatus);
      expect(kvs.get).toHaveBeenCalledWith(`license-cache-${mockCloudId}`);
    });

    it('should fetch fresh license info when cache is expired', async () => {
      kvs.get.mockResolvedValue(null); // No cache
      
      const mockInstallations = [{
        key: 'ai-requirements-expansion',
        enabled: true,
        version: '1.0.0'
      }];

      requestJira.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockInstallations)
      });

      const result = await service.validateLicense(mockCloudId);

      expect(result.isValid).toBe(true);
      expect(result.status).toBe('active');
      expect(result.tier).toBe('premium');
      expect(result.cloudId).toBe(mockCloudId);
      expect(kvs.set).toHaveBeenCalled(); // Should cache the result
    });

    it('should handle license validation errors with grace period', async () => {
      kvs.get.mockResolvedValue(null); // No cache
      requestJira.mockRejectedValue(new Error('API Error'));

      // Mock last valid license within grace period
      const lastValidLicense = {
        isValid: true,
        status: 'active',
        checkedAt: Date.now() - 60000 // 1 minute ago
      };
      
      kvs.get.mockImplementation((key) => {
        if (key.includes('last-valid')) {
          return Promise.resolve(lastValidLicense);
        }
        return Promise.resolve(null);
      });

      const result = await service.validateLicense(mockCloudId);

      expect(result.isValid).toBe(true);
      expect(result.status).toBe('grace_period');
      expect(result.error).toBe('API Error');
    });

    it('should return unlicensed when no grace period available', async () => {
      kvs.get.mockResolvedValue(null); // No cache or last valid license
      requestJira.mockRejectedValue(new Error('API Error'));

      const result = await service.validateLicense(mockCloudId);

      expect(result.isValid).toBe(false);
      expect(result.status).toBe('validation_failed');
      expect(result.error).toBe('API Error');
    });
  });

  describe('enforceLicense', () => {
    it('should allow operation for valid license with required feature', () => {
      const licenseStatus = {
        isValid: true,
        status: 'active',
        features: {
          aiRequirementsExpansion: true
        }
      };

      const result = service.enforceLicense(licenseStatus, 'testOperation', 'aiRequirementsExpansion', 'TEST-123');

      expect(createLicenseAllow).toHaveBeenCalled();
      expect(logLicenseEnforcement).toHaveBeenCalledWith('testOperation', 'TEST-123', expect.any(Object), licenseStatus);
      expect(result.allowed).toBe(true);
    });

    it('should block operation for invalid license', () => {
      const licenseStatus = {
        isValid: false,
        status: 'unlicensed'
      };

      const result = service.enforceLicense(licenseStatus, 'testOperation', 'aiRequirementsExpansion');

      expect(createLicenseBlock).toHaveBeenCalledWith('unlicensed', null, service.getUpgradeUrl());
      expect(result.allowed).toBe(false);
    });

    it('should block operation when feature is not available', () => {
      const licenseStatus = {
        isValid: true,
        status: 'active',
        features: {
          aiRequirementsExpansion: false
        }
      };

      const result = service.enforceLicense(licenseStatus, 'testOperation', 'aiRequirementsExpansion');

      expect(createLicenseBlock).toHaveBeenCalledWith(
        'feature_not_available',
        'The aiRequirementsExpansion feature is not available in your current license tier.',
        service.getUpgradeUrl()
      );
      expect(result.allowed).toBe(false);
    });

    it('should handle missing license status', () => {
      const result = service.enforceLicense(null, 'testOperation', 'aiRequirementsExpansion');

      expect(createLicenseBlock).toHaveBeenCalledWith(
        'license_check_failed',
        'Unable to verify license status. Please try again later.'
      );
      expect(result.allowed).toBe(false);
    });
  });

  describe('isFeatureAvailable', () => {
    it('should return true when feature is enabled', () => {
      const licenseStatus = {
        features: {
          aiRequirementsExpansion: true
        }
      };

      const result = service.isFeatureAvailable(licenseStatus, 'aiRequirementsExpansion');
      expect(result).toBe(true);
    });

    it('should return false when feature is disabled', () => {
      const licenseStatus = {
        features: {
          aiRequirementsExpansion: false
        }
      };

      const result = service.isFeatureAvailable(licenseStatus, 'aiRequirementsExpansion');
      expect(result).toBe(false);
    });

    it('should return false when license status is missing', () => {
      const result = service.isFeatureAvailable(null, 'aiRequirementsExpansion');
      expect(result).toBe(false);
    });

    it('should return false when features object is missing', () => {
      const licenseStatus = {};
      const result = service.isFeatureAvailable(licenseStatus, 'aiRequirementsExpansion');
      expect(result).toBe(false);
    });
  });

  describe('clearLicenseCache', () => {
    it('should clear both cache and last valid license', async () => {
      kvs.delete.mockResolvedValue(true);

      const result = await service.clearLicenseCache(mockCloudId);

      expect(result).toBe(true);
      expect(kvs.delete).toHaveBeenCalledWith(`license-cache-${mockCloudId}`);
      expect(kvs.delete).toHaveBeenCalledWith(`license-cache-last-valid-${mockCloudId}`);
    });

    it('should handle cache clear errors', async () => {
      kvs.delete.mockRejectedValue(new Error('Delete failed'));

      const result = await service.clearLicenseCache(mockCloudId);

      expect(result).toBe(false);
    });
  });

  describe('getAvailableFeatures', () => {
    it('should return all features enabled for licensed installation', () => {
      const features = service.getAvailableFeatures(true, {});

      expect(features).toEqual({
        aiRequirementsExpansion: true,
        confluenceIntegration: true,
        advancedTemplates: true,
        bulkProcessing: true,
        prioritySupport: true
      });
    });

    it('should return all features disabled for unlicensed installation', () => {
      const features = service.getAvailableFeatures(false, {});

      expect(features).toEqual({
        aiRequirementsExpansion: false,
        confluenceIntegration: false,
        advancedTemplates: false,
        bulkProcessing: false,
        prioritySupport: false
      });
    });
  });

  describe('determineLicenseTier', () => {
    it('should return premium for installed and enabled app', () => {
      const licenseInfo = {
        installed: true,
        enabled: true
      };

      const tier = service.determineLicenseTier(licenseInfo);
      expect(tier).toBe('premium');
    });

    it('should return free for not installed or disabled app', () => {
      const licenseInfo = {
        installed: false,
        enabled: false
      };

      const tier = service.determineLicenseTier(licenseInfo);
      expect(tier).toBe('free');
    });
  });
});
