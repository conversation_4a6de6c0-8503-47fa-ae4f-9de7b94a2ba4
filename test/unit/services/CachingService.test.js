/**
 * Unit tests for CachingService
 */

import { jest } from '@jest/globals';

// Mock KVS
jest.unstable_mockModule('@forge/kvs', () => ({
  kvs: {
    get: jest.fn(),
    set: jest.fn(),
    delete: jest.fn()
  }
}));

// Import after mocking
const { CachingService } = await import('../../../src/services/CachingService.js');
const { kvs } = await import('@forge/kvs');

describe('CachingService', () => {
  let cachingService;

  beforeEach(() => {
    cachingService = new CachingService();

    // Reset all mocks
    jest.clearAllMocks();

    kvs.get.mockResolvedValue(null);
    kvs.set.mockResolvedValue(undefined);
    kvs.delete.mockResolvedValue(undefined);
  });

  describe('generateCacheKey', () => {
    it('should generate cache key from pattern', () => {
      const pattern = 'cache-{type}-project-{projectId}-page-{pageId}';
      const params = {
        type: 'content',
        projectId: 'proj-123',
        pageId: 'page-456'
      };

      const result = cachingService.generateCacheKey(pattern, params);

      expect(result).toBe('cache-content-project-proj-123-page-page-456');
    });

    it('should handle missing parameters gracefully', () => {
      const pattern = 'cache-{type}-project-{projectId}';
      const params = { type: 'content' };

      const result = cachingService.generateCacheKey(pattern, params);

      expect(result).toBe('cache-content-project-{projectId}');
    });
  });

  describe('isCacheValid', () => {
    it('should return true when versions match', () => {
      const cachedData = { version: '5', data: {} };
      const currentVersion = '5';

      const result = cachingService.isCacheValid(cachedData, currentVersion);

      expect(result).toBe(true);
    });

    it('should return true when numeric versions match', () => {
      const cachedData = { version: 5, data: {} };
      const currentVersion = 5;

      const result = cachingService.isCacheValid(cachedData, currentVersion);

      expect(result).toBe(true);
    });

    it('should return true when mixed numeric/string versions match', () => {
      const cachedData = { version: '5', data: {} };
      const currentVersion = 5;

      const result = cachingService.isCacheValid(cachedData, currentVersion);

      expect(result).toBe(true);
    });

    it('should return false when versions do not match', () => {
      const cachedData = { version: '5', data: {} };
      const currentVersion = '6';

      const result = cachingService.isCacheValid(cachedData, currentVersion);

      expect(result).toBe(false);
    });

    it('should return false when cached data is null', () => {
      const result = cachingService.isCacheValid(null, '5');

      expect(result).toBe(false);
    });

    it('should return false when cached data has no version', () => {
      const cachedData = { data: {} };
      const currentVersion = '5';

      const result = cachingService.isCacheValid(cachedData, currentVersion);

      expect(result).toBe(false);
    });

    it('should return false when current version is undefined', () => {
      const cachedData = { version: '5', data: {} };

      const result = cachingService.isCacheValid(cachedData, undefined);

      expect(result).toBe(false);
    });
  });

  describe('getCachedPageContent', () => {
    const pageId = 'page-123';
    const projectId = 'proj-456';
    const currentVersion = 5;
    const expectedKey = 'cache-content-project-proj-456-page-page-123';

    it('should return cached data when version matches', async () => {
      const mockPageData = { id: pageId, title: 'Test Page' };
      const cachedData = {
        data: mockPageData,
        version: currentVersion,
        cached: Date.now()
      };

      kvs.get.mockResolvedValue(cachedData);

      const result = await cachingService.getCachedPageContent(pageId, projectId, currentVersion);

      expect(kvs.get).toHaveBeenCalledWith(expectedKey);
      expect(result).toEqual(mockPageData);
    });

    it('should return null when version does not match', async () => {
      const cachedData = {
        data: { id: pageId, title: 'Test Page' },
        version: 4, // Different version
        cached: Date.now()
      };

      kvs.get.mockResolvedValue(cachedData);

      const result = await cachingService.getCachedPageContent(pageId, projectId, currentVersion);

      expect(result).toBeNull();
    });

    it('should return null when no cached data exists', async () => {
      kvs.get.mockResolvedValue(null);

      const result = await cachingService.getCachedPageContent(pageId, projectId, currentVersion);

      expect(result).toBeNull();
    });

    it('should handle KVS errors gracefully', async () => {
      kvs.get.mockRejectedValue(new Error('Storage error'));

      const result = await cachingService.getCachedPageContent(pageId, projectId, currentVersion);

      expect(result).toBeNull();
    });
  });

  describe('setCachedPageContent', () => {
    const pageId = 'page-123';
    const projectId = 'proj-456';
    const mockPageData = {
      id: pageId,
      title: 'Test Page',
      version: { number: 5 }
    };

    it('should cache page data successfully', async () => {
      const result = await cachingService.setCachedPageContent(pageId, projectId, mockPageData);

      const expectedKey = 'cache-content-project-proj-456-page-page-123';
      const expectedCacheData = {
        data: mockPageData,
        version: 5,
        cached: expect.any(Number)
      };

      expect(kvs.set).toHaveBeenCalledWith(expectedKey, expectedCacheData);
      expect(result).toBe(true);
    });

    it('should handle missing version gracefully', async () => {
      const pageDataWithoutVersion = {
        id: pageId,
        title: 'Test Page'
      };

      const result = await cachingService.setCachedPageContent(pageId, projectId, pageDataWithoutVersion);

      const expectedCacheData = {
        data: pageDataWithoutVersion,
        version: 1, // Default version
        cached: expect.any(Number)
      };

      expect(kvs.set).toHaveBeenCalledWith(expect.any(String), expectedCacheData);
      expect(result).toBe(true);
    });

    it('should return false for invalid parameters', async () => {
      const result = await cachingService.setCachedPageContent(null, projectId, mockPageData);

      expect(kvs.set).not.toHaveBeenCalled();
      expect(result).toBe(false);
    });

    it('should handle KVS errors gracefully', async () => {
      kvs.set.mockRejectedValue(new Error('Storage error'));

      const result = await cachingService.setCachedPageContent(pageId, projectId, mockPageData);

      expect(result).toBe(false);
    });
  });

  describe('getCachedPageSummary', () => {
    const pageId = 'page-123';
    const projectId = 'proj-456';
    const currentVersion = 5;
    const expectedKey = 'cache-summary-project-proj-456-page-page-123';

    it('should return cached summary when version matches', async () => {
      const mockSummary = 'This page is about testing functionality';
      const cachedData = {
        summary: mockSummary,
        version: currentVersion,
        cached: Date.now()
      };

      kvs.get.mockResolvedValue(cachedData);

      const result = await cachingService.getCachedPageSummary(pageId, projectId, currentVersion);

      expect(kvs.get).toHaveBeenCalledWith(expectedKey);
      expect(result).toBe(mockSummary);
    });

    it('should return null when version does not match', async () => {
      const cachedData = {
        summary: 'Old summary',
        version: 4, // Different version
        cached: Date.now()
      };

      kvs.get.mockResolvedValue(cachedData);

      const result = await cachingService.getCachedPageSummary(pageId, projectId, currentVersion);

      expect(result).toBeNull();
    });
  });

  describe('setCachedPageSummary', () => {
    const pageId = 'page-123';
    const projectId = 'proj-456';
    const summary = 'This page describes the testing process';
    const pageVersion = 5;

    it('should cache page summary successfully', async () => {
      const result = await cachingService.setCachedPageSummary(pageId, projectId, summary, pageVersion);

      const expectedKey = 'cache-summary-project-proj-456-page-page-123';
      const expectedCacheData = {
        summary: summary,
        version: pageVersion,
        cached: expect.any(Number)
      };

      expect(kvs.set).toHaveBeenCalledWith(expectedKey, expectedCacheData);
      expect(result).toBe(true);
    });

    it('should return false for invalid parameters', async () => {
      const result = await cachingService.setCachedPageSummary(null, projectId, summary, pageVersion);

      expect(kvs.set).not.toHaveBeenCalled();
      expect(result).toBe(false);
    });
  });

  describe('getCachedPageContents', () => {
    it('should return cached and uncached page IDs', async () => {
      const pageIds = ['page-1', 'page-2', 'page-3'];
      const projectId = 'proj-123';
      const versionMap = {
        'page-1': 1,
        'page-2': 2,
        'page-3': 3
      };

      // Mock page-1 as cached, others as uncached
      kvs.get.mockImplementation((key) => {
        if (key.includes('page-1')) {
          return Promise.resolve({
            data: { id: 'page-1', title: 'Cached Page' },
            version: 1
          });
        }
        return Promise.resolve(null);
      });

      const result = await cachingService.getCachedPageContents(pageIds, projectId, versionMap);

      expect(result.cached).toHaveLength(1);
      expect(result.cached[0].id).toBe('page-1');
      expect(result.uncachedIds).toEqual(['page-2', 'page-3']);
    });
  });

  describe('invalidatePageCache', () => {
    const pageId = 'page-123';
    const projectId = 'proj-456';

    it('should delete both content and summary cache entries', async () => {
      const result = await cachingService.invalidatePageCache(pageId, projectId);

      const expectedContentKey = 'cache-content-project-proj-456-page-page-123';
      const expectedSummaryKey = 'cache-summary-project-proj-456-page-page-123';

      expect(kvs.delete).toHaveBeenCalledTimes(2);
      expect(kvs.delete).toHaveBeenCalledWith(expectedContentKey);
      expect(kvs.delete).toHaveBeenCalledWith(expectedSummaryKey);
      expect(result).toBe(true);
    });

    it('should handle KVS errors gracefully', async () => {
      kvs.delete.mockRejectedValue(new Error('Storage error'));

      const result = await cachingService.invalidatePageCache(pageId, projectId);

      expect(result).toBe(false);
    });
  });
});