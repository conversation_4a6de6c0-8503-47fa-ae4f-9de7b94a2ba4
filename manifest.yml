modules:
  jira:customField:
    - key: prompt-field
      name: Prompt
      description: Input prompt for LLM requirement expansion.
      type: string
      readOnly: false
      isInline: true
  jira:projectPage:
    - key: ai-settings-project
      resource: admin-page
      title: AI Requirements Settings
      resolver:
        function: admin-resolver
      conditions:
        - condition: user_is_project_admin
  function:
    - key: field-updated-handler # Handler for issue updates and creation
      handler: index.promptUpdateHandler
    - key: requirements-processor
      handler: index.requirementsProcessorHandler
      timeoutSeconds: 900
    - key: admin-resolver
      handler: index.adminHandler
  trigger:
    # Trigger module for issue updates and creation
    - key: field-updated-trigger
      function: field-updated-handler
      events:
        - avi:jira:updated:issue
        - avi:jira:created:issue
  consumer:
    - key: requirements-consumer
      queue: requirements-queue
      resolver:
        function: requirements-processor
        method: requirementsProcessorHandler

app:
  runtime:
    name: nodejs22.x
  id: ari:cloud:ecosystem::app/1855cc49-eb1c-46b8-b8d7-04e25dbd5aa8
  licensing:
    enabled: true
permissions:
  scopes:
    - read:jira-work
    - write:jira-work
    - storage:app
    - read:space:confluence
    - read:page:confluence
    - read:confluence-space.summary
    - read:confluence-content.summary
    - search:confluence
  content:
    styles:
      - 'unsafe-inline'
  external:
    fetch:
      backend:
        - address: https://openrouter.ai

resources:
  - key: admin-page
    path: static/admin-page/build
