# Licensing System Documentation

## Overview

This document describes the comprehensive licensing system implemented for the AI Requirements Expansion Forge app to prepare it for Atlassian Marketplace distribution. The system provides strict license validation, enforcement, and user messaging to ensure compliance with marketplace requirements.

## Key Changes Made

Based on your requirements, the following changes were implemented:

1. **No License Tab**: Removed the license tab from the admin panel
2. **Admin Panel Lockdown**: Admin panel shows licensing overlay for unlicensed users with marketplace link
3. **No Free Tier**: All features require a valid license (no free tier, only trial period handled by Atlassian)
4. **No Customer Messages**: License enforcement blocks functionality without adding messages to customer Jira issues
5. **No Grace Period**: Strict enforcement with no grace period (users could clear cache weekly)
6. **Extended Caching**: 2-hour cache with hourly renewal attempts (effectively 1-hour grace period if cache was populated)
7. **Removed Invalid Scopes**: Removed non-existent `read:app-entitlements:jira` scope from manifest

## Architecture

### Core Components

#### 1. LicenseService (`src/services/LicenseService.js`)
The central service that handles all license-related operations:

- **License Validation**: Checks license status with Atlassian APIs
- **Caching**: Implements 5-minute cache with grace period fallback
- **Feature Management**: Controls access to premium features
- **Enforcement**: Blocks or allows operations based on license status

#### 2. License Utilities (`src/utils/license-errors.js`)
Provides consistent error handling and messaging:

- **ADF Message Creation**: Generates Atlassian Document Format content for license messages
- **Standard Messages**: Predefined license-related error messages
- **Logging**: Centralized license enforcement logging

#### 3. UI Components (`static/admin-page/src/components/LicenseStatus.jsx`)
React component for displaying license information in the admin interface:

- **License Status Display**: Shows current license tier and status
- **Feature Availability**: Lists enabled/disabled features
- **Upgrade Prompts**: Provides links to upgrade license

## How It Works

### 1. License Validation Flow

```
User Action → License Check → Cache Check → API Call → Validation → Enforcement
```

1. **Trigger**: Any protected operation (AI expansion, admin settings, etc.)
2. **Cache Check**: First checks 5-minute cache for recent validation
3. **API Validation**: If cache miss, calls Atlassian APIs to verify license
4. **Grace Period**: On API failure, uses last valid license for 7 days
5. **Enforcement**: Allows/blocks operation based on license status

### 2. Feature-Based Access Control

The system defines these premium features:
- `aiRequirementsExpansion`: Core AI functionality
- `confluenceIntegration`: Confluence context integration
- `advancedTemplates`: Custom issue type templates
- `bulkProcessing`: Batch operations
- `prioritySupport`: Enhanced support access

### 3. Integration Points

#### Resolvers (`src/resolvers/index.js`)
- **promptUpdateHandler**: Validates license before processing prompt updates
- **requirementsProcessorHandler**: Checks license before AI expansion
- **adminHandler**: Enforces license for advanced admin features

#### Admin Interface
- **License Tab**: New tab showing license status and features
- **Feature Restrictions**: Blocks premium features for unlicensed users
- **Upgrade Prompts**: Contextual upgrade messaging

## Configuration

### Manifest Changes (`manifest.yml`)
```yaml
app:
  licensing:
    enabled: true

permissions:
  scopes:
    - read:app-data:jira
    - read:app-entitlements:jira
```

### Environment Variables
```bash
# License validation settings (optional overrides)
LICENSE_CACHE_TTL=300000          # 5 minutes
LICENSE_GRACE_PERIOD=604800000    # 7 days
```

## License Tiers and Features

### Free Tier
- Basic issue field functionality
- Limited AI processing
- Standard templates only

### Premium Tier (Licensed)
- Full AI requirements expansion
- Confluence integration
- Advanced custom templates
- Bulk processing capabilities
- Priority support

## Error Handling

### Grace Period Logic
When license validation fails (network issues, API downtime):
1. Check for last valid license within 7-day grace period
2. If found, continue with full functionality
3. Display grace period warning to users
4. If grace period expired, restrict to free tier

### User Messaging
- **ADF Format**: All license messages use Atlassian Document Format
- **Contextual**: Messages appear directly in Jira issues/admin interface
- **Actionable**: Include upgrade links and clear next steps

## Testing

### Unit Tests (`test/unit/services/LicenseService.test.js`)
- License validation scenarios
- Feature enforcement logic
- Cache behavior
- Grace period handling
- Error conditions

### Integration Testing
Run existing integration tests to ensure licensing doesn't break core functionality:
```bash
npm run test:integration
```

## Deployment Checklist

### Pre-Marketplace Submission
1. ✅ License validation implemented
2. ✅ Feature enforcement active
3. ✅ User messaging in place
4. ✅ Admin interface updated
5. ✅ Tests passing
6. ✅ Documentation complete

### Marketplace Configuration
1. Set up app licensing in Atlassian Developer Console
2. Configure pricing tiers and features
3. Update app description with feature details
4. Test with marketplace licensing APIs

## Monitoring and Maintenance

### Logging
The system logs all license enforcement actions:
```javascript
// Example log output
{
  "operation": "aiExpansion",
  "issueKey": "PROJ-123",
  "allowed": false,
  "reason": "unlicensed",
  "licenseStatus": "unlicensed",
  "tier": "free",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Cache Management
- Automatic cache expiration (5 minutes)
- Manual cache clearing via admin interface
- Grace period storage for reliability

## Troubleshooting

### Common Issues

1. **License validation fails**
   - Check network connectivity
   - Verify app permissions in manifest
   - Review Atlassian API status

2. **Features incorrectly blocked**
   - Clear license cache
   - Check license tier configuration
   - Verify feature mapping

3. **Grace period not working**
   - Check last valid license storage
   - Verify grace period duration
   - Review error handling logic

### Debug Commands
```bash
# Clear license cache for testing
forge logs --tail  # Monitor license validation logs
```

## Future Enhancements

### Planned Features
- Usage analytics and reporting
- License expiration warnings
- Automatic license renewal prompts
- Multi-tier feature granularity
- Enterprise-specific features

### API Evolution
The licensing system is designed to adapt to future Atlassian licensing API changes with minimal code modifications.
