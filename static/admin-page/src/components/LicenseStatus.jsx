import React, { useState, useEffect } from 'react';
import { invoke } from '@forge/bridge';
import { 
  Box, 
  Text, 
  Badge, 
  <PERSON><PERSON>, 
  <PERSON>,
  <PERSON>,
  <PERSON>ack,
  Inline
} from '@forge/react';

const LicenseStatus = () => {
  const [licenseStatus, setLicenseStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadLicenseStatus();
  }, []);

  const loadLicenseStatus = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await invoke('admin-resolver', {
        method: 'getLicenseStatus',
        payload: {}
      });

      if (response.success) {
        setLicenseStatus(response.licenseStatus);
      } else {
        setError(response.error || 'Failed to load license status');
      }
    } catch (err) {
      console.error('Failed to load license status:', err);
      setError('Failed to load license status');
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status, tier) => {
    const statusConfig = {
      'active': { appearance: 'success', text: 'Active' },
      'grace_period': { appearance: 'warning', text: 'Grace Period' },
      'unlicensed': { appearance: 'removed', text: 'Unlicensed' },
      'validation_failed': { appearance: 'removed', text: 'Validation Failed' }
    };

    const config = statusConfig[status] || { appearance: 'default', text: status };
    
    return (
      <Inline space="space.050">
        <Badge appearance={config.appearance} text={config.text} />
        {tier && <Badge appearance="default" text={tier.charAt(0).toUpperCase() + tier.slice(1)} />}
      </Inline>
    );
  };

  const formatDate = (timestamp) => {
    if (!timestamp) return 'N/A';
    return new Date(timestamp).toLocaleDateString();
  };

  const getFeatureStatus = (features) => {
    if (!features) return [];
    
    return Object.entries(features).map(([key, enabled]) => ({
      name: key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()),
      enabled
    }));
  };

  if (loading) {
    return (
      <Box padding="space.200">
        <Inline space="space.100" alignBlock="center">
          <Spinner size="small" />
          <Text>Loading license status...</Text>
        </Inline>
      </Box>
    );
  }

  if (error) {
    return (
      <Box padding="space.200">
        <Banner appearance="error">
          <Text>{error}</Text>
          <Button text="Retry" onClick={loadLicenseStatus} />
        </Banner>
      </Box>
    );
  }

  if (!licenseStatus) {
    return (
      <Box padding="space.200">
        <Banner appearance="warning">
          <Text>No license information available</Text>
        </Banner>
      </Box>
    );
  }

  const featureList = getFeatureStatus(licenseStatus.features);

  return (
    <Box padding="space.200">
      <Stack space="space.200">
        <Text size="large" weight="bold">License Status</Text>
        
        {/* License Status Overview */}
        <Box padding="space.150" backgroundColor="color.background.neutral.subtle" borderRadius="3px">
          <Stack space="space.100">
            <Inline space="space.200" alignBlock="center">
              <Text weight="medium">Status:</Text>
              {getStatusBadge(licenseStatus.status, licenseStatus.tier)}
            </Inline>
            
            {licenseStatus.expiresAt && (
              <Inline space="space.200" alignBlock="center">
                <Text weight="medium">Expires:</Text>
                <Text>{formatDate(licenseStatus.expiresAt)}</Text>
              </Inline>
            )}
            
            {licenseStatus.gracePeriodEnd && licenseStatus.status === 'grace_period' && (
              <Inline space="space.200" alignBlock="center">
                <Text weight="medium">Grace Period Ends:</Text>
                <Text>{formatDate(licenseStatus.gracePeriodEnd)}</Text>
              </Inline>
            )}
          </Stack>
        </Box>

        {/* License Warnings */}
        {!licenseStatus.isValid && (
          <Banner appearance="warning">
            <Stack space="space.100">
              <Text weight="bold">License Required</Text>
              <Text>
                Some features may be limited or unavailable without a valid license.
              </Text>
              {licenseStatus.upgradeUrl && (
                <Button 
                  text="Upgrade License" 
                  appearance="primary"
                  onClick={() => window.open(licenseStatus.upgradeUrl, '_blank')}
                />
              )}
            </Stack>
          </Banner>
        )}

        {licenseStatus.status === 'grace_period' && (
          <Banner appearance="warning">
            <Stack space="space.100">
              <Text weight="bold">Grace Period Active</Text>
              <Text>
                License validation is temporarily unavailable. Full functionality will continue until {formatDate(licenseStatus.gracePeriodEnd)}.
              </Text>
            </Stack>
          </Banner>
        )}

        {/* Feature Availability */}
        <Stack space="space.100">
          <Text weight="medium">Feature Availability</Text>
          <Box padding="space.150" backgroundColor="color.background.neutral.subtle" borderRadius="3px">
            <Stack space="space.075">
              {featureList.map((feature, index) => (
                <Inline key={index} space="space.100" alignBlock="center">
                  <Badge 
                    appearance={feature.enabled ? 'success' : 'removed'} 
                    text={feature.enabled ? '✓' : '✗'} 
                  />
                  <Text>{feature.name}</Text>
                </Inline>
              ))}
            </Stack>
          </Box>
        </Stack>

        {/* Refresh Button */}
        <Inline space="space.100">
          <Button text="Refresh Status" onClick={loadLicenseStatus} />
        </Inline>
      </Stack>
    </Box>
  );
};

export default LicenseStatus;
