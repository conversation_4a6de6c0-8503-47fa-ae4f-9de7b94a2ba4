import React from 'react';
import { createRoot } from 'react-dom/client';
import { invoke, view } from '@forge/bridge';
import MDEditor from '@uiw/react-md-editor';
import './styles.css';


function AdminApp() {
    const [activeTab, setActiveTab] = React.useState('templates');
    const [licenseStatus, setLicenseStatus] = React.useState(null);
    const [licenseLoading, setLicenseLoading] = React.useState(true);

    // Check license status on app load
    React.useEffect(() => {
        checkLicenseStatus();
    }, []);

    const checkLicenseStatus = async () => {
        try {
            setLicenseLoading(true);
            const response = await invoke('admin-resolver', {
                method: 'getLicenseStatus',
                payload: {}
            });

            if (response.success) {
                setLicenseStatus(response.licenseStatus);
            } else {
                setLicenseStatus({ isValid: false, status: 'unknown' });
            }
        } catch (error) {
            console.error('Failed to check license status:', error);
            setLicenseStatus({ isValid: false, status: 'unknown' });
        } finally {
            setLicenseLoading(false);
        }
    };

    // Show licensing overlay if not licensed
    if (licenseLoading) {
        return (
            <div className="admin-container">
                <div className="loading-message">
                    <p>Loading...</p>
                </div>
            </div>
        );
    }

    if (!licenseStatus?.isValid) {
        return (
            <div className="admin-container">
                <LicensingOverlay />
            </div>
        );
    }

    return (
        <div className="admin-container">
            <TabNavigation activeTab={activeTab} onTabChange={setActiveTab} />
            {activeTab === 'settings' && <SettingsView />}
            {activeTab === 'templates' && <TemplatesView />}
            {activeTab === 'confluence' && <ConfluenceView />}
            <StatusMessage />
        </div>
    );
}

function LicensingOverlay() {
    const upgradeUrl = 'https://marketplace.atlassian.com/apps/your-app-key'; // Replace with actual marketplace URL

    return (
        <div className="licensing-overlay">
            <div className="licensing-content">
                <h2>License Required</h2>
                <p>
                    This app requires a valid license to access admin features.
                    Please subscribe to continue using advanced functionality.
                </p>
                <div className="licensing-actions">
                    <a
                        href={upgradeUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="upgrade-button"
                    >
                        Subscribe on Atlassian Marketplace
                    </a>
                </div>
                <div className="licensing-note">
                    <p>
                        <small>
                            After subscribing, please refresh this page to access admin features.
                        </small>
                    </p>
                </div>
            </div>
        </div>
    );
}

function TabNavigation({ activeTab, onTabChange }) {
    const tabs = [
        { id: 'templates', label: 'Templates', description: 'Edit issue type templates' },
        { id: 'settings', label: 'Settings', description: 'Configure AI settings' },
        { id: 'confluence', label: 'Project Context', description: 'Configure Confluence project context' }
    ];
    
    return (
        <div className="tab-navigation">
            <div className="tab-list">
                {tabs.map(tab => (
                    <button
                        key={tab.id}
                        className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
                        onClick={() => onTabChange(tab.id)}
                        aria-selected={activeTab === tab.id}
                    >
                        {tab.label}
                    </button>
                ))}
            </div>
        </div>
    );
}

function TemplatesView() {
    const [currentIssueType, setCurrentIssueType] = React.useState('story');
    const [templates, setTemplates] = React.useState({});
    const [currentTemplate, setCurrentTemplate] = React.useState('');
    const [hasUnsavedChanges, setHasUnsavedChanges] = React.useState(false);
    const [isLoading, setIsLoading] = React.useState(true);
    const [availableIssueTypes, setAvailableIssueTypes] = React.useState([]);
    const [confirmModal, setConfirmModal] = React.useState({ isOpen: false, type: '', data: null });
    const [outputFields, setOutputFields] = React.useState({});
    const [currentOutputField, setCurrentOutputField] = React.useState('');
    const [paragraphFields, setParagraphFields] = React.useState([]);
    const [fieldConfigurationData, setFieldConfigurationData] = React.useState({});
    const [hasUnsavedOutputFields, setHasUnsavedOutputFields] = React.useState(false);
    const [projectId, setProjectId] = React.useState(null);
    
    // Load project context and issue types on component mount
    React.useEffect(() => {
        loadProjectContext();
    }, []);
    
    const loadProjectContext = async () => {
        setIsLoading(true);
        
        try {
            const contextResponse = await invoke('admin-resolver', {
                method: 'getCurrentProjectContext'
            });
            
            if (!contextResponse?.success || !contextResponse?.projectId) {
                throw new Error('Could not determine project context');
            }
            
            setProjectId(contextResponse.projectId);
            await loadProjectIssueTypes(contextResponse.projectId);

        } catch (error) {
            console.error('Failed to load project context:', error.message);
            setAvailableIssueTypes([]);
        } finally {
            // Always set loading to false, whether success or error
            setIsLoading(false);
        }
    };
    
    const loadProjectIssueTypes = async (projectId) => {
        try {
            const response = await invoke('admin-resolver', {
                method: 'getProjectIssueTypes',
                payload: { projectId }
            });

            if (response && response.success && response.issueTypes) {
                // Sort issue types in specific order
                const priorityOrder = ['story', 'bug', 'task', 'subtask', 'epic'];
                const sortedIssueTypes = response.issueTypes.sort((a, b) => {
                    const aName = a.name.toLowerCase();
                    const bName = b.name.toLowerCase();

                    const aIndex = priorityOrder.indexOf(aName);
                    const bIndex = priorityOrder.indexOf(bName);

                    if (aIndex !== -1 && bIndex !== -1) {
                        return aIndex - bIndex;
                    }

                    if (aIndex !== -1) return -1;
                    if (bIndex !== -1) return 1;

                    return a.name.localeCompare(b.name);
                });

                setAvailableIssueTypes(sortedIssueTypes);

                const issueTypeNames = sortedIssueTypes.map(it => it.name);

                // Load both templates and field configuration in parallel, but wait for both
                await Promise.all([
                    loadTemplatesForIssueTypes(issueTypeNames),
                    loadFieldConfigurationForIssueTypes(sortedIssueTypes)
                ]);

            } else {
                throw new Error('Failed to load issue types');
            }
        } catch (error) {
            console.error('Failed to load issue types:', error);
            throw error;
        }
    };
    
    const loadTemplatesForIssueTypes = async (issueTypeNames) => {
        try {
            const response = await invoke('admin-resolver', {
                method: 'getTemplatesForIssueTypes',
                payload: { issueTypes: issueTypeNames }
            });

            if (response && response.success && response.templates) {
                setTemplates(response.templates);

                const firstIssueType = issueTypeNames.length > 0 ? issueTypeNames[0] : '';
                const firstTemplate = response.templates[firstIssueType] || '';

                setCurrentIssueType(firstIssueType);
                setCurrentTemplate(firstTemplate);
            } else {
                setCurrentTemplate('');
            }
        } catch (error) {
            console.error('Failed to load templates:', error);
            setCurrentTemplate('');
        }
        // Note: isLoading is now set to false in loadProjectIssueTypes after both operations complete
    };

    const loadFieldConfigurationForIssueTypes = async (issueTypesArray) => {
        try {
            const response = await invoke('admin-resolver', {
                method: 'getFieldConfigurationForIssueTypes',
                payload: { issueTypes: issueTypesArray }
            });
            
            if (response && response.success && response.issueTypeFieldData) {
                const fieldData = response.issueTypeFieldData;
                
                // Extract output fields mapping
                const outputFields = {};
                for (const [issueTypeName, data] of Object.entries(fieldData)) {
                    outputFields[issueTypeName] = data.selectedOutputField || '';
                }
                setOutputFields(outputFields);
                
                // Set current state for the first issue type
                const firstIssueType = issueTypesArray.length > 0 ? issueTypesArray[0].name : '';
                if (firstIssueType && fieldData[firstIssueType]) {
                    setCurrentOutputField(fieldData[firstIssueType].selectedOutputField || '');
                    setParagraphFields(fieldData[firstIssueType].paragraphFields || []);
                } else {
                    setCurrentOutputField('');
                    setParagraphFields([]);
                }
                
                // Store the complete field data for later use when switching issue types
                setFieldConfigurationData(fieldData);
            } else {
                setOutputFields({});
                setCurrentOutputField('');
                setParagraphFields([]);
                setFieldConfigurationData({});
            }
        } catch (error) {
            console.error('Failed to load field configuration:', error);
            setOutputFields({});
            setCurrentOutputField('');
            setParagraphFields([]);
            setFieldConfigurationData({});
        }
    };

    // Update current state when issue type changes
    React.useEffect(() => {
        if (currentIssueType && fieldConfigurationData[currentIssueType]) {
            const issueTypeData = fieldConfigurationData[currentIssueType];
            setCurrentOutputField(issueTypeData.selectedOutputField || '');
            setParagraphFields(issueTypeData.paragraphFields || []);
        }
    }, [currentIssueType, fieldConfigurationData]);
    
    const handleIssueTypeChange = (newIssueType) => {
        if (hasUnsavedChanges || hasUnsavedOutputFields) {
            const userConfirmed = window.confirm(
                'You have unsaved changes that will be lost if you switch issue types. Do you want to continue?'
            );
            
            if (!userConfirmed) {
                return;
            }
        }
        
        // Save current template and output field to memory before switching
        const updatedTemplates = { ...templates };
        updatedTemplates[currentIssueType] = currentTemplate;
        setTemplates(updatedTemplates);
        
        const updatedOutputFields = { ...outputFields };
        updatedOutputFields[currentIssueType] = currentOutputField;
        setOutputFields(updatedOutputFields);
        
        // Switch to new issue type
        setCurrentIssueType(newIssueType);
        setCurrentTemplate(templates[newIssueType] || '');
        setCurrentOutputField(outputFields[newIssueType] || '');
        setHasUnsavedChanges(false);
        setHasUnsavedOutputFields(false);
    };
    
    const handleTemplateChange = (value) => {
        setCurrentTemplate(value);
        if (!hasUnsavedChanges) {
            setHasUnsavedChanges(true);
        }
    };

    const handleOutputFieldChange = (value) => {
        setCurrentOutputField(value);
        if (!hasUnsavedOutputFields) {
            setHasUnsavedOutputFields(true);
        }
    };
    
    const handleSave = async () => {
        const template = currentTemplate.trim();
        
        if (!template) {
            showStatus('error', '❌ Template cannot be empty');
            return;
        }
        
        showStatus('info', `💾 Saving ${currentIssueType} configuration...`);
        
        try {
            // Save template
            const templateResponse = await invoke('admin-resolver', {
                method: 'saveTemplateByType',
                payload: {
                    issueType: currentIssueType,
                    template: template
                }
            });
            
            if (!templateResponse || !templateResponse.success) {
                showStatus('error', `❌ ${templateResponse?.error || 'Unknown error saving template'}`);
                return;
            }

            // Save output field if changed
            if (hasUnsavedOutputFields) {
                const outputFieldsToSave = {};
                outputFieldsToSave[currentIssueType] = currentOutputField;
                
                const outputFieldResponse = await invoke('admin-resolver', {
                    method: 'saveOutputFieldsForIssueTypes',
                    payload: {
                        outputFields: outputFieldsToSave
                    }
                });
                
                if (!outputFieldResponse || !outputFieldResponse.success) {
                    showStatus('error', `❌ ${outputFieldResponse?.error || 'Unknown error saving output field'}`);
                    return;
                }
            }
            
            // Update state on success
            const updatedTemplates = { ...templates };
            updatedTemplates[currentIssueType] = template;
            setTemplates(updatedTemplates);
            
            const updatedOutputFields = { ...outputFields };
            updatedOutputFields[currentIssueType] = currentOutputField;
            setOutputFields(updatedOutputFields);
            
            setHasUnsavedChanges(false);
            setHasUnsavedOutputFields(false);
            showStatus('success', `✅ ${currentIssueType} configuration saved successfully!`);
        } catch (error) {
            console.error('Failed to save configuration:', error);
            showStatus('error', `❌ Error saving configuration: ${error.message || 'Unknown error'}`);
        }
    };
    
    const handleReset = () => {
        setConfirmModal({
            isOpen: true,
            type: 'resetCurrent',
            data: { issueType: currentIssueType }
        });
    };
    
    const performResetCurrent = async (issueType) => {
        showStatus('info', `🔄 Resetting ${issueType} template to default...`);
        
        try {
            const response = await invoke('admin-resolver', {
                method: 'resetTemplateToDefault',
                payload: { issueType }
            });
            
            if (response && response.success && response.template !== undefined) {
                setCurrentTemplate(response.template);
                
                const updatedTemplates = { ...templates };
                updatedTemplates[issueType] = response.template;
                setTemplates(updatedTemplates);
                
                setHasUnsavedChanges(false);
                showStatus('success', `✅ ${issueType} template reset to default!`);
            } else {
                showStatus('error', `❌ ${response?.error || 'Unknown error'}`);
            }
        } catch (error) {
            console.error('Failed to reset template:', error);
            showStatus('error', `❌ Error resetting template: ${error.message || 'Unknown error'}`);
        }
    };
    
    const handleResetAll = () => {
        setConfirmModal({
            isOpen: true,
            type: 'resetAll',
            data: {}
        });
    };
    
    const performResetAll = async () => {
        showStatus('info', '🔄 Resetting all templates to defaults...');
        
        try {
            const response = await invoke('admin-resolver', {
                method: 'resetTemplatesToDefaults'
            });
            
            if (response && response.success) {
                setTemplates({});
                
                const issueTypeNames = availableIssueTypes.map(it => it.name);
                await loadTemplatesForIssueTypes(issueTypeNames);
                
                showStatus('success', '✅ All templates reset to defaults successfully!');
            } else {
                showStatus('error', `❌ ${response?.error || 'Unknown error'}`);
            }
        } catch (error) {
            console.error('Failed to reset all templates:', error);
            showStatus('error', `❌ Error resetting templates: ${error.message || 'Unknown error'}`);
        }
    };
    
    const handleConfirmModal = async () => {
        const modalType = confirmModal.type;
        const modalData = confirmModal.data;
        
        setConfirmModal({ isOpen: false, type: '', data: null });
        
        try {
            if (modalType === 'resetCurrent') {
                await performResetCurrent(modalData.issueType);
            } else if (modalType === 'resetAll') {
                await performResetAll();
            }
        } catch (error) {
            console.error('Modal operation failed:', error);
            showStatus('error', `❌ Operation failed: ${error.message || 'Unknown error'}`);
        }
    };
    
    const handleCancelModal = () => {
        setConfirmModal({ isOpen: false, type: '', data: null });
    };
    
    if (isLoading) {
        return (
            <div className="template-card">
                <div className="loading-state">
                    <p>Loading templates...</p>
                </div>
            </div>
        );
    }
    
    return (
        <div className="template-card">
            <TemplateHeader 
                currentIssueType={currentIssueType}
                onIssueTypeChange={handleIssueTypeChange}
                availableIssueTypes={availableIssueTypes}
                currentOutputField={currentOutputField}
                onOutputFieldChange={handleOutputFieldChange}
                paragraphFields={paragraphFields}
            />
            
            <p className="card-description">Define the structure for AI-expanded requirements. Use {'{curly brackets}'} for placeholders or instructions.</p>
            
            <TemplateEditor 
                value={currentTemplate}
                onChange={handleTemplateChange}
            />
            
            {(hasUnsavedChanges || hasUnsavedOutputFields) && (
                <div className="unsaved-warning">
                    ⚠️ You have unsaved changes that will be lost if you switch issue types.
                </div>
            )}
            
            <ButtonGroup 
                onSave={handleSave}
                onReset={handleReset}
                onResetAll={handleResetAll}
                hasUnsavedChanges={hasUnsavedChanges || hasUnsavedOutputFields}
            />
            
            <ConfirmationModal
                isOpen={confirmModal.isOpen}
                onClose={handleCancelModal}
                onConfirm={handleConfirmModal}
                title={
                    confirmModal.type === 'resetCurrent' 
                        ? `Reset ${confirmModal.data?.issueType || ''} Template`
                        : 'Reset All Templates'
                }
                message={
                    confirmModal.type === 'resetCurrent'
                        ? `Are you sure you want to reset the ${confirmModal.data?.issueType || ''} template to default? This action cannot be undone.`
                        : 'Are you sure you want to reset ALL templates to defaults? This will clear all customized templates, even for issue types that may have been removed from your project. This action cannot be undone.'
                }
                confirmText={
                    confirmModal.type === 'resetCurrent' ? 'Reset Template' : 'Reset All Templates'
                }
                cancelText="Cancel"
                isDangerous={true}
            />
        </div>
    );
}

function SettingsView() {
    const [settings, setSettings] = React.useState({
        aiModel: '',
        temperature: '0.7',
        maxTokens: '1000'
    });
    const [hasUnsavedChanges, setHasUnsavedChanges] = React.useState(false);
    const [isLoading, setIsLoading] = React.useState(true);
    
    // Load settings on component mount
    React.useEffect(() => {
        loadSettings();
    }, []);
    
    const loadSettings = async () => {
        setIsLoading(true);
        
        try {
            const response = await invoke('admin-resolver', {
                method: 'getSettings'
            });
            
            if (response && response.success && response.settings) {
                setSettings(response.settings);
            } else {
                showStatus('info', '💡 Using default settings');
            }
        } catch (error) {
            console.error('Failed to load settings:', error);
            showStatus('error', '❌ Error loading settings');
        } finally {
            setIsLoading(false);
        }
    };
    
    const handleSettingsChange = (field, value) => {
        setSettings(prev => ({
            ...prev,
            [field]: value
        }));
        
        if (!hasUnsavedChanges) {
            setHasUnsavedChanges(true);
        }
    };
    
    const handleSave = async () => {
        showStatus('info', '💾 Saving settings...');
        
        try {
            const response = await invoke('admin-resolver', {
                method: 'saveSettings',
                payload: {
                    settings: settings
                }
            });
            
            if (response && response.success) {
                setHasUnsavedChanges(false);
                showStatus('success', '✅ Settings saved successfully!');
            } else {
                showStatus('error', `❌ ${response?.error || 'Unknown error'}`);
            }
        } catch (error) {
            console.error('Failed to save settings:', error);
            showStatus('error', `❌ Error saving settings: ${error.message || 'Unknown error'}`);
        }
    };
    
    const handleReset = async () => {
        const confirmReset = window.confirm('Are you sure you want to reset all settings to defaults? This action cannot be undone.');
        if (!confirmReset) return;
        
        showStatus('info', '🔄 Resetting settings to defaults...');
        
        try {
            const response = await invoke('admin-resolver', {
                method: 'resetToDefaults'
            });
            
            if (response && response.success && response.settings) {
                setSettings(response.settings);
                setHasUnsavedChanges(false);
                showStatus('success', '✅ Settings reset to defaults successfully!');
            } else {
                showStatus('error', `❌ ${response?.error || 'Unknown error'}`);
            }
        } catch (error) {
            console.error('Failed to reset settings:', error);
            showStatus('error', `❌ Error resetting settings: ${error.message || 'Unknown error'}`);
        }
    };
    
    if (isLoading) {
        return (
            <div className="settings-card">
                <div className="loading-state">
                    <p>Loading settings...</p>
                </div>
            </div>
        );
    }
    
    return (
        <div className="settings-card">
            <div className="settings-header">
                <h2 className="card-title">AI Configuration</h2>
                <p className="card-description">Configure the AI model and parameters used for requirements expansion.</p>
            </div>
            
            <div className="settings-form">
                <div className="form-group">
                    <label htmlFor="aiModel" className="form-label">AI Model</label>
                    <input
                        type="text"
                        id="aiModel"
                        className="form-input"
                        value={settings.aiModel}
                        onChange={(e) => handleSettingsChange('aiModel', e.target.value)}
                        placeholder="e.g., deepseek/deepseek-chat-v3-0324:free"
                    />
                    <p className="form-help">The AI model identifier used for requirements expansion.</p>
                </div>
                
                <div className="form-group">
                    <label htmlFor="temperature" className="form-label">Temperature</label>
                    <div className="temperature-control">
                        <input
                            type="range"
                            id="temperature"
                            className="form-range"
                            min="0"
                            max="2"
                            step="0.1"
                            value={settings.temperature}
                            onChange={(e) => handleSettingsChange('temperature', e.target.value)}
                        />
                        <input
                            type="number"
                            className="form-input temperature-input"
                            min="0"
                            max="2"
                            step="0.1"
                            value={settings.temperature}
                            onChange={(e) => handleSettingsChange('temperature', e.target.value)}
                        />
                    </div>
                    <p className="form-help">Controls randomness: lower values = more focused, higher values = more creative.</p>
                </div>
                
                <div className="form-group">
                    <label htmlFor="maxTokens" className="form-label">Max Tokens</label>
                    <input
                        type="number"
                        id="maxTokens"
                        className="form-input"
                        min="1"
                        max="4000"
                        value={settings.maxTokens}
                        onChange={(e) => handleSettingsChange('maxTokens', e.target.value)}
                    />
                    <p className="form-help">Maximum number of tokens in the AI response (1-4000).</p>
                </div>
            </div>
            
            {hasUnsavedChanges && (
                <div className="unsaved-warning">
                    ⚠️ You have unsaved changes that will be lost if you switch tabs.
                </div>
            )}
            
            <div className="button-group">
                <button 
                    className="btn-primary"
                    onClick={handleSave}
                >
                    {hasUnsavedChanges ? 'Save Settings *' : 'Save Settings'}
                </button>
                <button 
                    className="btn-secondary"
                    onClick={handleReset}
                >
                    Reset to Defaults
                </button>
            </div>
        </div>
    );
}

function ConfluenceView() {
    const [spaces, setSpaces] = React.useState([]);
    const [isLoading, setIsLoading] = React.useState(true);
    
    // State for spaces and pages lists
    const [configuredSpaces, setConfiguredSpaces] = React.useState([]);
    const [configuredPages, setConfiguredPages] = React.useState([]);
    const [addSelectedSpaceKey, setAddSelectedSpaceKey] = React.useState('');
    const [addPages, setAddPages] = React.useState([]);
    
    // Load initial data
    React.useEffect(() => {
        loadConfluenceData();
    }, []);
    
    const loadConfluenceData = async () => {
        setIsLoading(true);
        
        try {
            // Load spaces and configured lists in parallel
            const [spacesResponse, spacesListResponse, pagesListResponse] = await Promise.all([
                invoke('admin-resolver', { method: 'getConfluenceSpaces' }),
                invoke('admin-resolver', { method: 'getConfluenceSpacesList' }),
                invoke('admin-resolver', { method: 'getConfluencePagesList' })
            ]);
            
            if (spacesResponse && spacesResponse.success) {
                setSpaces(spacesResponse.spaces);
            }
            
            // Load configured spaces and pages lists
            if (spacesListResponse && spacesListResponse.success) {
                setConfiguredSpaces(spacesListResponse.spaces);
            }
            
            if (pagesListResponse && pagesListResponse.success) {
                setConfiguredPages(pagesListResponse.pages);
            }
        } catch (error) {
            console.error('Failed to load Confluence data:', error);
            showStatus('error', '❌ Error loading Confluence data');
        } finally {
            setIsLoading(false);
        }
    };
    
    const loadPagesForAddSpace = async (spaceKey) => {
        if (!spaceKey) {
            setAddPages([]);
            return;
        }
        
        try {
            const response = await invoke('admin-resolver', {
                method: 'getConfluencePages',
                payload: { spaceKey, limit: 50 }
            });
            
            if (response && response.success) {
                setAddPages(response.pages);
            } else {
                setAddPages([]);
                showStatus('error', '❌ Failed to load pages for this space');
            }
        } catch (error) {
            console.error('Failed to load pages:', error);
            setAddPages([]);
            showStatus('error', '❌ Error loading pages');
        }
    };
    
    
    // Handler to add space immediately when selected
    const handleAddSpace = async (spaceKey) => {
        if (!spaceKey) {
            return;
        }
        
        const selectedSpace = spaces.find(space => space.key === spaceKey);
        if (!selectedSpace) {
            showStatus('error', '❌ Selected space not found');
            return;
        }
        
        showStatus('info', '💾 Adding space to configuration...');
        
        try {
            const response = await invoke('admin-resolver', {
                method: 'addConfluenceSpace',
                payload: {
                    spaceKey: selectedSpace.key,
                    spaceName: selectedSpace.name
                }
            });
            
            if (response && response.success) {
                // Reload the configured spaces list
                const spacesListResponse = await invoke('admin-resolver', { method: 'getConfluenceSpacesList' });
                if (spacesListResponse && spacesListResponse.success) {
                    setConfiguredSpaces(spacesListResponse.spaces);
                }
                
                showStatus('success', '✅ Space added to configuration successfully');
            } else {
                showStatus('error', `❌ ${response.error || 'Failed to add space'}`);
            }
        } catch (error) {
            console.error('Failed to add space:', error);
            showStatus('error', '❌ Error adding space');
        }
    };
    
    const handleRemoveSpace = async (spaceKey) => {
        showStatus('info', '🗑️ Removing space from configuration...');
        
        try {
            const response = await invoke('admin-resolver', {
                method: 'removeConfluenceSpace',
                payload: { spaceKey }
            });
            
            if (response && response.success) {
                // Reload the configured spaces list
                const spacesListResponse = await invoke('admin-resolver', { method: 'getConfluenceSpacesList' });
                if (spacesListResponse && spacesListResponse.success) {
                    setConfiguredSpaces(spacesListResponse.spaces);
                }
                
                showStatus('success', '✅ Space removed from configuration successfully');
            } else {
                showStatus('error', `❌ ${response.error || 'Failed to remove space'}`);
            }
        } catch (error) {
            console.error('Failed to remove space:', error);
            showStatus('error', '❌ Error removing space');
        }
    };
    
    const handleAddSpaceChange = (spaceKey) => {
        setAddSelectedSpaceKey(spaceKey);
        loadPagesForAddSpace(spaceKey);
    };
    
    const handleAddPage = async (pageId) => {
        const selectedPage = addPages.find(page => page.id === pageId);
        if (!selectedPage) {
            showStatus('error', '❌ Selected page not found');
            return;
        }
        
        showStatus('info', '💾 Adding page to configuration...');
        
        try {
            const response = await invoke('admin-resolver', {
                method: 'addConfluencePage',
                payload: {
                    pageId: selectedPage.id,
                    pageTitle: selectedPage.title,
                    spaceKey: addSelectedSpaceKey
                }
            });
            
            if (response && response.success) {
                // Reload the configured pages list
                const pagesListResponse = await invoke('admin-resolver', { method: 'getConfluencePagesList' });
                if (pagesListResponse && pagesListResponse.success) {
                    setConfiguredPages(pagesListResponse.pages);
                }
                
                showStatus('success', '✅ Page added to configuration successfully');
            } else {
                showStatus('error', `❌ ${response.error || 'Failed to add page'}`);
            }
        } catch (error) {
            console.error('Failed to add page:', error);
            showStatus('error', '❌ Error adding page');
        }
    };
    
    const handleRemovePage = async (pageId) => {
        showStatus('info', '🗑️ Removing page from configuration...');
        
        try {
            const response = await invoke('admin-resolver', {
                method: 'removeConfluencePage',
                payload: { pageId }
            });
            
            if (response && response.success) {
                // Reload the configured pages list
                const pagesListResponse = await invoke('admin-resolver', { method: 'getConfluencePagesList' });
                if (pagesListResponse && pagesListResponse.success) {
                    setConfiguredPages(pagesListResponse.pages);
                }
                
                showStatus('success', '✅ Page removed from configuration successfully');
            } else {
                showStatus('error', `❌ ${response.error || 'Failed to remove page'}`);
            }
        } catch (error) {
            console.error('Failed to remove page:', error);
            showStatus('error', '❌ Error removing page');
        }
    };
    
    const handleClearAll = async () => {
        const confirmed = window.confirm(
            'Are you sure you want to clear ALL configured spaces and pages? This action cannot be undone.'
        );
        
        if (!confirmed) return;
        
        showStatus('info', '🗑️ Clearing all Confluence configuration...');
        
        try {
            const response = await invoke('admin-resolver', {
                method: 'clearAllConfluenceConfiguration'
            });
            
            if (response && response.success) {
                setConfiguredSpaces([]);
                setConfiguredPages([]);
                showStatus('success', '✅ All Confluence configuration cleared successfully');
            } else {
                showStatus('error', `❌ ${response.error || 'Failed to clear all configuration'}`);
            }
        } catch (error) {
            console.error('Failed to clear all configuration:', error);
            showStatus('error', '❌ Error clearing all configuration');
        }
    };
    
    if (isLoading) {
        return (
            <div className="settings-card">
                <div className="loading-state">
                    <p>Loading Confluence data...</p>
                </div>
            </div>
        );
    }
    
    return (
        <div className="settings-card">
            <div className="settings-header">
                <h2 className="card-title">Confluence Project Context Configuration</h2>
                <p className="card-description">
                    Configure Confluence spaces and pages that provide context for AI-powered requirements expansion.
                </p>
            </div>
            
            {/* Spaces and Pages Lists */}
            <div className="management-sections">
                {/* Search Confluence Spaces Section */}
                <div className="management-section">
                    <h3 className="section-title">Search Confluence Spaces</h3>
                    <p className="section-description">
                        Configure spaces that the AI can search through for additional context.
                    </p>
                    
                    <div className="add-control">
                        <select
                            className="form-select add-select"
                            value=""
                            onChange={(e) => e.target.value && handleAddSpace(e.target.value)}
                        >
                            <option value="">Select a space to add...</option>
                            {spaces
                                .filter(space => !configuredSpaces.some(cs => cs.key === space.key))
                                .map(space => (
                                <option key={space.key} value={space.key}>
                                    {space.name} ({space.key})
                                </option>
                            ))}
                        </select>
                    </div>
                    
                    {configuredSpaces.length > 0 && (
                        <div className="configured-list">
                            <h4 className="list-title">Configured Spaces:</h4>
                            {configuredSpaces.map(space => (
                                <div key={space.key} className="list-item">
                                    <span className="item-name">{space.name} ({space.key})</span>
                                    <button 
                                        className="remove-button"
                                        onClick={() => handleRemoveSpace(space.key)}
                                        title="Remove space"
                                    >
                                        ×
                                    </button>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
                
                {/* Project Overview Pages Section */}
                <div className="management-section">
                    <h3 className="section-title">Project Overview Pages</h3>
                    <p className="section-description">
                        Select specific pages that the AI will always unclude as project context.
                    </p>
                    
                    <div className="add-control">
                        <select
                            className="form-select add-select"
                            value={addSelectedSpaceKey}
                            onChange={(e) => handleAddSpaceChange(e.target.value)}
                        >
                            <option value="">Select a space...</option>
                            {spaces.map(space => (
                                <option key={space.key} value={space.key}>
                                    {space.name} ({space.key})
                                </option>
                            ))}
                        </select>
                        
                        {addSelectedSpaceKey && (
                            <select
                                className="form-select add-select"
                                value=""
                                onChange={(e) => e.target.value && handleAddPage(e.target.value)}
                            >
                                <option value="">Select a page to add...</option>
                                {addPages
                                    .filter(page => !configuredPages.some(cp => cp.pageId === page.id))
                                    .map(page => (
                                    <option key={page.id} value={page.id}>
                                        {page.title}
                                    </option>
                                ))}
                            </select>
                        )}
                    </div>
                    
                    {configuredPages.length > 0 && (
                        <div className="configured-list">
                            <h4 className="list-title">Configured Pages:</h4>
                            {configuredPages.map(page => (
                                <div key={page.pageId} className="list-item">
                                    <span className="item-name">{page.title}</span>
                                    <button 
                                        className="remove-button"
                                        onClick={() => handleRemovePage(page.pageId)}
                                        title="Remove page"
                                    >
                                        ×
                                    </button>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </div>
            
            {/* Clear All Configuration Button */}
            <div className="button-group clear-all-section">
                <button 
                    className="btn-danger"
                    onClick={handleClearAll}
                    disabled={configuredSpaces.length === 0 && configuredPages.length === 0}
                >
                    Clear All Configuration
                </button>
            </div>
        </div>
    );
}

function TemplateHeader({ currentIssueType, onIssueTypeChange, availableIssueTypes, currentOutputField, onOutputFieldChange, paragraphFields }) {
    // Helper to get field display name with fallback indicator
    const getFieldDisplayName = (field) => {
        return field.name;
    };

    // Get default field name with fallback logic
    const getDefaultFieldName = () => {
        // First try to find "AI Requirements" field
        const aiRequirementsField = paragraphFields.find(f => f.name === 'AI Requirements');
        if (aiRequirementsField) {
            return 'AI Requirements (default)';
        }
        
        // Fall back to Description field
        const descriptionField = paragraphFields.find(f => f.name === 'Description');
        if (descriptionField) {
            return 'Description (fallback)';
        }
        
        return 'Default field not available';
    };

    return (
        <div className="template-header">
            <div className="control-group">
                <label htmlFor="issueTypeSelect" className="issue-type-label">Issue Type:</label>
                <select 
                    id="issueTypeSelect"
                    className="issue-type-select"
                    value={currentIssueType}
                    onChange={(e) => onIssueTypeChange(e.target.value)}
                >
                    {availableIssueTypes.map(issueType => (
                        <option key={issueType.id} value={issueType.name}>
                            {issueType.name}
                        </option>
                    ))}
                </select>
            </div>
            
            <div className="control-group">
                <label htmlFor="outputFieldSelect" className="issue-type-label">Output Field:</label>
                <select 
                    id="outputFieldSelect"
                    className="issue-type-select"
                    value={currentOutputField}
                    onChange={(e) => onOutputFieldChange(e.target.value)}
                >
                    <option value="">
                        {getDefaultFieldName()}
                    </option>
                    {paragraphFields
                        .filter(field => {
                            // Exclude fields that are already shown as default
                            const aiRequirementsField = paragraphFields.find(f => f.name === 'AI Requirements');
                            const descriptionField = paragraphFields.find(f => f.name === 'Description');
                            
                            if (aiRequirementsField && field.name === 'AI Requirements') {
                                return false; // AI Requirements is shown as default
                            }
                            if (!aiRequirementsField && descriptionField && field.name === 'Description') {
                                return false; // Description is shown as fallback default
                            }
                            return true;
                        })
                        .map(field => (
                        <option key={field.id} value={field.name}>
                            {getFieldDisplayName(field)}
                        </option>
                    ))}
                </select>
            </div>
        </div>
    );
}

function TemplateEditor({ value, onChange }) {
    return (
        <div className="editor-container">
            <MDEditor
                value={value}
                onChange={(newValue) => onChange(newValue || '')}
                height={400}
                preview="live"
                hideToolbar={false}
                visibleDragBar={false}
                data-color-mode="light"
                placeholder="Loading template..."
            />
        </div>
    );
}

function ButtonGroup({ onSave, onReset, onResetAll, hasUnsavedChanges }) {
    return (
        <div className="button-group">
            <button 
                className="btn-primary"
                onClick={onSave}
            >
                {hasUnsavedChanges ? 'Save Template *' : 'Save Template'}
            </button>
            <button 
                className="btn-secondary"
                onClick={onReset}
            >
                Reset Current to Default
            </button>
            <button 
                className="btn-danger"
                onClick={onResetAll}
            >
                Reset All Templates
            </button>
        </div>
    );
}

function ConfirmationModal({ isOpen, onClose, onConfirm, title, message, confirmText = "Confirm", cancelText = "Cancel", isDangerous = false }) {
    if (!isOpen) return null;
    
    return (
        <div className="modal-overlay" onClick={onClose}>
            <div className="modal-content" onClick={(e) => e.stopPropagation()}>
                <div className="modal-header">
                    <h3 className="modal-title">{title}</h3>
                </div>
                <div className="modal-body">
                    <p className="modal-message">{message}</p>
                </div>
                <div className="modal-footer">
                    <button 
                        className="btn-secondary" 
                        onClick={onClose}
                    >
                        {cancelText}
                    </button>
                    <button 
                        className={isDangerous ? "btn-danger" : "btn-primary"}
                        onClick={onConfirm}
                    >
                        {confirmText}
                    </button>
                </div>
            </div>
        </div>
    );
}

function StatusMessage() {
    const [status, setStatus] = React.useState({ type: '', message: '' });
    
    React.useEffect(() => {
        window.showStatus = (type, message) => {
            setStatus({ type, message });
            
            if (type === 'success' || type === 'info') {
                setTimeout(() => {
                    setStatus({ type: '', message: '' });
                }, 5000);
            }
        };
    }, []);
    
    if (!status.message) return null;
    
    return (
        <div className="status-message">
            <div className={`status-${status.type}`}>
                {status.message}
            </div>
        </div>
    );
}

function showStatus(type, message) {
    if (window.showStatus) {
        window.showStatus(type, message);
    }
}

document.addEventListener('DOMContentLoaded', async function() {
    const root = document.getElementById('root');
    if (!root) {
        console.error('Root element not found');
        return;
    }
    
    try {
        await view.theme.enable();
        const reactRoot = createRoot(root);
        reactRoot.render(<AdminApp />);
    } catch (err) {
        console.warn('Failed to enable theming, continuing without design tokens:', err);
        const reactRoot = createRoot(root);
        reactRoot.render(<AdminApp />);
    }
});