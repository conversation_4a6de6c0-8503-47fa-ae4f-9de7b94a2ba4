/**
 * License Service - Handles license validation and enforcement for marketplace apps
 * 
 * This service provides comprehensive license validation for Atlassian Forge apps
 * distributed through the Atlassian Marketplace. It handles:
 * - License status checking
 * - User entitlement validation
 * - License enforcement with graceful degradation
 * - Caching for performance optimization
 */

import { requestJira } from '@forge/api';
import { kvs } from '@forge/kvs';
import { config } from '../config/index.js';
import { createLicenseBlock, createLicenseAllow, logLicenseEnforcement } from '../utils/license-errors.js';

export class LicenseService {
  constructor() {
    this.cachePrefix = 'license-cache-';
    this.cacheTTL = 5 * 60 * 1000; // 5 minutes cache
    this.gracePeriod = 7 * 24 * 60 * 60 * 1000; // 7 days grace period
  }

  /**
   * Check if the current installation has a valid license
   * @param {string} cloudId - The Atlassian cloud ID
   * @returns {Promise<Object>} License validation result
   */
  async validateLicense(cloudId) {
    try {
      // Check cache first
      const cachedResult = await this.getCachedLicenseStatus(cloudId);
      if (cachedResult) {
        console.log(`License cache hit for cloud ID: ${cloudId}`);
        return cachedResult;
      }

      // Fetch license information from Atlassian
      const licenseInfo = await this.fetchLicenseInfo(cloudId);
      const validationResult = this.processLicenseInfo(licenseInfo, cloudId);

      // Cache the result
      await this.cacheLicenseStatus(cloudId, validationResult);

      console.log(`License validation completed for cloud ID: ${cloudId}`, {
        isValid: validationResult.isValid,
        status: validationResult.status,
        tier: validationResult.tier
      });

      return validationResult;
    } catch (error) {
      console.error('License validation failed:', error);
      
      // Return grace period result on error
      return await this.handleLicenseValidationError(cloudId, error);
    }
  }

  /**
   * Fetch license information from Atlassian APIs
   * @param {string} cloudId - The Atlassian cloud ID
   * @returns {Promise<Object>} Raw license information
   */
  async fetchLicenseInfo(cloudId) {
    try {
      // Try to get app installation info first
      const installationResponse = await requestJira('/rest/atlassian-connect/1/addons/installed', {
        method: 'GET'
      });

      if (!installationResponse.ok) {
        throw new Error(`Failed to fetch installation info: ${installationResponse.status}`);
      }

      const installations = await installationResponse.json();
      const currentApp = installations.find(app => app.key === this.getAppKey());

      if (!currentApp) {
        throw new Error('Current app installation not found');
      }

      // For Forge apps, we need to check entitlements differently
      // This is a placeholder for the actual entitlement check
      return {
        installed: true,
        enabled: currentApp.enabled,
        version: currentApp.version,
        cloudId: cloudId,
        // Additional license fields would be populated here
        // based on actual Atlassian licensing APIs
      };
    } catch (error) {
      console.error('Failed to fetch license info:', error);
      throw error;
    }
  }

  /**
   * Process raw license information into validation result
   * @param {Object} licenseInfo - Raw license information
   * @param {string} cloudId - The Atlassian cloud ID
   * @returns {Object} Processed license validation result
   */
  processLicenseInfo(licenseInfo, cloudId) {
    const now = Date.now();
    
    // Basic validation - app is installed and enabled
    const isInstalled = licenseInfo.installed === true;
    const isEnabled = licenseInfo.enabled === true;
    
    // For now, we'll consider the app licensed if it's installed and enabled
    // In a real marketplace scenario, you'd check actual license status here
    const isLicensed = isInstalled && isEnabled;
    
    return {
      isValid: isLicensed,
      status: isLicensed ? 'active' : 'unlicensed',
      tier: this.determineLicenseTier(licenseInfo),
      cloudId: cloudId,
      checkedAt: now,
      expiresAt: licenseInfo.expiresAt || null,
      gracePeriodEnd: now + this.gracePeriod,
      features: this.getAvailableFeatures(isLicensed, licenseInfo),
      metadata: {
        version: licenseInfo.version,
        installed: isInstalled,
        enabled: isEnabled
      }
    };
  }

  /**
   * Determine the license tier based on license information
   * @param {Object} licenseInfo - License information
   * @returns {string} License tier
   */
  determineLicenseTier(licenseInfo) {
    // This would be based on actual license data from Atlassian
    // For now, we'll use a simple heuristic
    if (licenseInfo.installed && licenseInfo.enabled) {
      return 'premium'; // Assume premium for installed apps
    }
    return 'free';
  }

  /**
   * Get available features based on license status
   * @param {boolean} isLicensed - Whether the installation is licensed
   * @param {Object} licenseInfo - License information
   * @returns {Object} Available features
   */
  getAvailableFeatures(isLicensed, licenseInfo) {
    if (!isLicensed) {
      return {
        aiRequirementsExpansion: false,
        confluenceIntegration: false,
        advancedTemplates: false,
        bulkProcessing: false,
        prioritySupport: false
      };
    }

    // Full features for licensed installations
    return {
      aiRequirementsExpansion: true,
      confluenceIntegration: true,
      advancedTemplates: true,
      bulkProcessing: true,
      prioritySupport: true
    };
  }

  /**
   * Handle license validation errors with grace period logic
   * @param {string} cloudId - The Atlassian cloud ID
   * @param {Error} error - The validation error
   * @returns {Promise<Object>} Grace period license result
   */
  async handleLicenseValidationError(cloudId, error) {
    const now = Date.now();

    // Check if we have a previous successful validation within grace period
    const lastValidLicense = await this.getLastValidLicense(cloudId);

    if (lastValidLicense && (now - lastValidLicense.checkedAt) < this.gracePeriod) {
      console.log(`Using grace period for cloud ID: ${cloudId}`);
      return {
        ...lastValidLicense,
        isValid: true,
        status: 'grace_period',
        gracePeriodEnd: lastValidLicense.checkedAt + this.gracePeriod,
        error: error.message
      };
    }

    // No valid grace period, return unlicensed
    return {
      isValid: false,
      status: 'validation_failed',
      tier: 'free',
      cloudId: cloudId,
      checkedAt: now,
      expiresAt: null,
      gracePeriodEnd: null,
      features: this.getAvailableFeatures(false, {}),
      error: error.message,
      metadata: {}
    };
  }

  /**
   * Get cached license status
   * @param {string} cloudId - The Atlassian cloud ID
   * @returns {Promise<Object|null>} Cached license status or null
   */
  async getCachedLicenseStatus(cloudId) {
    try {
      const cacheKey = `${this.cachePrefix}${cloudId}`;
      const cached = await kvs.get(cacheKey);

      if (!cached || !cached.checkedAt) {
        return null;
      }

      const now = Date.now();
      const age = now - cached.checkedAt;

      if (age > this.cacheTTL) {
        // Cache expired, clean it up
        await kvs.delete(cacheKey);
        return null;
      }

      return cached;
    } catch (error) {
      console.error('Failed to get cached license status:', error);
      return null;
    }
  }

  /**
   * Cache license status
   * @param {string} cloudId - The Atlassian cloud ID
   * @param {Object} licenseStatus - License status to cache
   * @returns {Promise<boolean>} Success status
   */
  async cacheLicenseStatus(cloudId, licenseStatus) {
    try {
      const cacheKey = `${this.cachePrefix}${cloudId}`;
      await kvs.set(cacheKey, licenseStatus);
      return true;
    } catch (error) {
      console.error('Failed to cache license status:', error);
      return false;
    }
  }

  /**
   * Get last valid license from storage
   * @param {string} cloudId - The Atlassian cloud ID
   * @returns {Promise<Object|null>} Last valid license or null
   */
  async getLastValidLicense(cloudId) {
    try {
      const lastValidKey = `${this.cachePrefix}last-valid-${cloudId}`;
      return await kvs.get(lastValidKey);
    } catch (error) {
      console.error('Failed to get last valid license:', error);
      return null;
    }
  }

  /**
   * Store last valid license for grace period
   * @param {string} cloudId - The Atlassian cloud ID
   * @param {Object} licenseStatus - Valid license status to store
   * @returns {Promise<boolean>} Success status
   */
  async storeLastValidLicense(cloudId, licenseStatus) {
    try {
      if (licenseStatus.isValid && licenseStatus.status === 'active') {
        const lastValidKey = `${this.cachePrefix}last-valid-${cloudId}`;
        await kvs.set(lastValidKey, licenseStatus);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to store last valid license:', error);
      return false;
    }
  }

  /**
   * Get the app key from manifest or environment
   * @returns {string} App key
   */
  getAppKey() {
    // This should match your app's key in the manifest
    // For now, we'll derive it from the app ID in the manifest
    return 'ai-requirements-expansion';
  }

  /**
   * Check if a specific feature is available for the current license
   * @param {Object} licenseStatus - License status object
   * @param {string} featureName - Name of the feature to check
   * @returns {boolean} Whether the feature is available
   */
  isFeatureAvailable(licenseStatus, featureName) {
    if (!licenseStatus || !licenseStatus.features) {
      return false;
    }
    return licenseStatus.features[featureName] === true;
  }

  /**
   * Enforce license for a specific operation
   * @param {Object} licenseStatus - License status object
   * @param {string} operation - Operation being performed
   * @param {string} featureName - Feature name required for the operation
   * @param {string} issueKey - Optional issue key for logging
   * @returns {Object} Enforcement result
   */
  enforceLicense(licenseStatus, operation, featureName, issueKey = null) {
    let enforcement;

    if (!licenseStatus) {
      enforcement = createLicenseBlock('license_check_failed', 'Unable to verify license status. Please try again later.');
    } else if (!licenseStatus.isValid) {
      enforcement = createLicenseBlock('unlicensed', null, this.getUpgradeUrl());
    } else if (featureName && !this.isFeatureAvailable(licenseStatus, featureName)) {
      enforcement = createLicenseBlock('feature_not_available', `The ${featureName} feature is not available in your current license tier.`, this.getUpgradeUrl());
    } else {
      enforcement = createLicenseAllow();
    }

    // Log enforcement action
    if (licenseStatus) {
      logLicenseEnforcement(operation, issueKey, enforcement, licenseStatus);
    }

    return enforcement;
  }

  /**
   * Get upgrade URL for the marketplace
   * @returns {string} Upgrade URL
   */
  getUpgradeUrl() {
    // This would be your app's marketplace URL
    return 'https://marketplace.atlassian.com/apps/your-app-key';
  }

  /**
   * Clear all license cache (useful for testing or troubleshooting)
   * @param {string} cloudId - The Atlassian cloud ID
   * @returns {Promise<boolean>} Success status
   */
  async clearLicenseCache(cloudId) {
    try {
      const cacheKey = `${this.cachePrefix}${cloudId}`;
      const lastValidKey = `${this.cachePrefix}last-valid-${cloudId}`;

      await Promise.all([
        kvs.delete(cacheKey),
        kvs.delete(lastValidKey)
      ]);

      console.log(`License cache cleared for cloud ID: ${cloudId}`);
      return true;
    } catch (error) {
      console.error('Failed to clear license cache:', error);
      return false;
    }
  }
}
