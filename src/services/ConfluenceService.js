/**
 * Confluence Service - Handles Confluence API operations and content management
 */

import { kvs } from '@forge/kvs';
import { convert as adfToMd } from 'adf-to-md';
import { config } from '../config/index.js';
import {
  getSpaces,
  getPagesByIds,
  getPageContent as getPageContentHelper,
  getAllPagesFromSpaces
} from '../utils/confluence-helpers.js';
import { CachingService } from './CachingService.js';
import { AIService } from './AIService.js';

export class ConfluenceService {
  constructor() {
    this.cachingService = new CachingService();
    this.aiService = new AIService();
  }


  /**
   * Get multiple pages with intelligent caching using page metadata
   * @param {Array} pageData - Array of page objects with metadata and version info
   * @param {string} projectId - Project ID for cache key
   * @returns {Promise<Array>} Array of page objects with content
   */
  async getPagesByIdsWithCache(pageData, projectId) {
    try {
      if (!pageData || pageData.length === 0) {
        return [];
      }

      // Use provided metadata to check cache
      console.log(`Checking cache for ${pageData.length} pages using metadata...`);
      const currentPages = pageData;
      
      // Create version map for cache validation
      const versionMap = {};
      const pageIds = [];
      currentPages.forEach(page => {
        const pageId = page.pageId || page.id;
        if (page && pageId) {
          versionMap[pageId] = page.version?.number || page.version || 1;
          pageIds.push(pageId);
        }
      });
      
      // Check cache using version-based validation
      const { cached, uncachedIds } = await this.cachingService.getCachedPageContents(pageIds, projectId, versionMap);
      
      console.log(`Cache check: Found ${cached.length} pages in cache, ${uncachedIds.length} need API fetch`);
      
      // Get uncached pages - need to fetch full content from API
      const uncachedPageIds = uncachedIds;
      const uncachedPages = uncachedPageIds.length > 0 ? await getPagesByIds(uncachedPageIds) : [];
      
      // Cache the newly fetched pages
      for (const pageData of uncachedPages) {
        if (pageData && pageData.id) {
          await this.cachingService.setCachedPageContent(pageData.id, projectId, pageData);
        }
      }
      
      // Combine cached and fetched pages
      const allPages = [...cached, ...uncachedPages];
      console.log(`Retrieved ${allPages.length} total pages (${cached.length} from cache + ${uncachedPages.length} from API)`);
      
      return allPages;
    } catch (error) {
      console.error('Error getting pages by IDs with cache:', error);
      return [];
    }
  }

  /**
   * Get all available Confluence spaces
   * @returns {Promise<Array>} Array of space objects
   */
  async getSpaces() {
    return await getSpaces();
  }




  /**
   * Extract markdown content from Confluence page
   * @param {Object} pageData - Page data from Confluence API
   * @returns {string} Markdown content
   */
  extractPlainTextContent(pageData) {
    try {
      if (!pageData || !pageData.body || !pageData.body.atlas_doc_format) {
        console.error('Page data or body is missing');
        return '';
      }

      // Get the ADF content
      let adfContent = pageData.body.atlas_doc_format.value;
      
      // Parse ADF content if it's a string
      if (typeof adfContent === 'string') {
        try {
          adfContent = JSON.parse(adfContent);
        } catch (parseError) {
          console.error('Error parsing ADF JSON:', parseError);
          return '';
        }
      }
      
      // Convert ADF to Markdown
      const markdown = adfToMd(adfContent).result || '';
      return markdown;
    } catch (error) {
      console.error('Error extracting markdown content:', error);
      return '';
    }
  }

  /**
   * Get AI-generated summary for a Confluence page with caching
   * @param {Object} pageData - Page object with version info and metadata
   * @param {string} projectId - Project ID for cache scoping
   * @returns {Promise<string|null>} Page summary or null if failed
   */
  async getPageSummaryWithCache(pageData, projectId) {
    let pageId = 'unknown';
    
    try {
      if (!pageData || !projectId) {
        throw new Error('Page data and project ID are required for summary generation');
      }
      
      pageId = pageData.pageId || pageData.id;
      const currentVersion = pageData.version?.number || pageData.version || 1;
      
      if (!pageId) {
        throw new Error('Page ID not found in page data');
      }

      // Check for cached summary first
      const cachedSummary = await this.cachingService.getCachedPageSummary(pageId, projectId, currentVersion);
      
      if (cachedSummary) {
        return cachedSummary;
      }

      // Need full content for summary generation - fetch if not available
      let fullPageData = pageData;
      if (!pageData.body || !pageData.body.atlas_doc_format) {
        fullPageData = await getPageContentHelper(pageId);
        if (!fullPageData) {
          console.error(`Could not fetch full content for page ${pageId}`);
          return null;
        }
      }

      // Generate new summary using AI
      const pageContent = this.extractPlainTextContent(fullPageData);
      
      let summary = null;
      if (!pageContent || pageContent.trim().length === 0) {
        console.log(`Page ${pageId} has no extractable content, caching empty summary`);
        await this.cachingService.setCachedPageSummary(pageId, projectId, '', currentVersion);
        summary = '';
      } else {
        summary = await this.aiService.generatePageSummary(fullPageData.title, pageContent, projectId);
      }
      
      if (summary !== null) {
        // Clean up token count references from the summary (with or without brackets)
        const cleanedSummary = summary.replace(/\(?\d+\s+tokens?\)?/gi, '').trim();
        
        // Cache the cleaned summary
        await this.cachingService.setCachedPageSummary(pageId, projectId, cleanedSummary, currentVersion);
        return cleanedSummary;
      }

      console.warn(`Failed to generate AI summary for page ${pageId}`);
      return null;
    } catch (error) {
      console.error(`Error getting page summary for ${pageId}:`, error);
      return null;
    }
  }

  /**
   * Get AI-generated summaries for multiple pages with caching
   * @param {Array} availablePages - Array of page objects with version info and metadata
   * @param {string} projectId - Project ID for cache scoping
   * @returns {Promise<Object>} Object with pageId -> summary mapping
   */
  async getPageSummariesBatch(availablePages, projectId) {
    const summaries = {};
    
    try {
      if (!availablePages || availablePages.length === 0) {
        return summaries;
      }

      console.log(`Generating summaries for ${availablePages.length} pages...`);

      // Process pages in parallel for better performance
      await Promise.all(
        availablePages.map(async (pageData) => {
          const summary = await this.getPageSummaryWithCache(pageData, projectId);
          const pageId = pageData.pageId || pageData.id;
          if (summary && pageId) {
            summaries[pageId] = summary;
          }
        })
      );

      console.log(`Generated ${Object.keys(summaries).length} summaries out of ${availablePages.length} pages`);
      return summaries;
    } catch (error) {
      console.error('Error generating batch page summaries:', error);
      return summaries;
    }
  }


  /**
   * Get project context using specific relevant pages with AI summaries (for use after separate AI relevancy analysis)
   * @param {string} projectId - Project ID
   * @param {Array} relevantPages - Array of page objects with metadata deemed relevant by AI
   * @returns {Promise<string>} Project context text or empty string
   */
  async getProjectContextWithRelevantPages(projectId, relevantPages) {
    try {
      // Always include project overview configured pages
      const configuredPages = await this.getConfiguredPages(projectId);
      
      // Extract page IDs for reference
      const projectOverviewPageIds = configuredPages.map(p => p.pageId || p.id);
      const relevantPageIds = relevantPages.map(p => p.pageId || p.id);
      
      // Create combined list of unique pages with metadata
      const pageMap = new Map();
      
      // Add configured pages to map
      configuredPages.forEach(page => {
        const pageId = page.pageId || page.id;
        pageMap.set(pageId, page);
      });
      
      // Add relevant pages to map
      relevantPages.forEach(page => {
        const pageId = page.pageId || page.id;
        pageMap.set(pageId, page);
      });
      
      const allPages = Array.from(pageMap.values());
      
      if (allPages.length === 0) {
        console.log(`No relevant Confluence pages for project ${projectId}`);
        return '';
      }

      // Fetch full page content for context generation using page metadata for cache optimization
      const pagesWithContent = await this.getPagesByIdsWithCache(allPages, projectId);
      
      // Process content for all pages
      const processedPages = pagesWithContent.map((pageData) => {
        try {
          if (!pageData) {
            return null;
          }
          
          const content = this.extractPlainTextContent(pageData);
          if (!content) {
            return null;
          }
          
          // Determine page classification
          const isProjectOverview = projectOverviewPageIds.includes(pageData.id);
          const isAiRecommended = relevantPageIds.includes(pageData.id);
          
          return {
            id: pageData.id,
            title: pageData.title || `Page ${pageData.id}`,
            content: content, // Full content for detailed context
            projectOverview: isProjectOverview,
            aiRecommended: isAiRecommended
          };
        } catch (error) {
          console.error(`Error processing page ${pageData.id}:`, error);
          return null;
        }
      }).filter(page => page !== null);

      // Return structured JSON context with full content
      const structuredContext = {
        summary: {
          totalPages: allPages.length,
          projectOverviewPages: projectOverviewPageIds.length,
          aiRecommendedPages: relevantPageIds.length
        },
        pages: processedPages
      };

      const allContext = JSON.stringify(structuredContext, null, 2);
      
      console.log(`Retrieved context from ${allPages.length} pages (${projectOverviewPageIds.length} project overview + ${relevantPageIds.length} AI-recommended) with full content`);
      return allContext;
    } catch (error) {
      console.error(`Error getting project context with relevant pages for ${projectId}:`, error);
      return '';
    }
  }






  /**
   * Get the configured Confluence spaces list for a project
   * @param {string} projectId - Project ID
   * @returns {Promise<Array>} Array of space configs [{key, name}] or empty array
   */
  async getConfiguredSpaces(projectId) {
    try {
      const storageKey = `confluence-spaces-project-${projectId}`;
      const spaces = await kvs.get(storageKey);
      return spaces || [];
    } catch (error) {
      console.error(`Error getting configured spaces for project ${projectId}:`, error);
      return [];
    }
  }

  /**
   * Add a space to the configured spaces list for a project
   * @param {string} projectId - Project ID
   * @param {string} spaceKey - Space key to add
   * @param {string} spaceName - Space name to add
   * @returns {Promise<boolean>} Success status
   */
  async addConfiguredSpace(projectId, spaceKey, spaceName) {
    try {
      if (!projectId || typeof projectId !== 'string') {
        throw new Error('Project ID must be a non-empty string');
      }
      
      if (!spaceKey || typeof spaceKey !== 'string') {
        throw new Error('Space key must be a non-empty string');
      }

      if (!spaceName || typeof spaceName !== 'string') {
        throw new Error('Space name must be a non-empty string');
      }

      const spaces = await this.getConfiguredSpaces(projectId);
      
      // Check if space already exists
      const existingSpace = spaces.find(space => space.key === spaceKey);
      if (existingSpace) {
        return true; // Already exists, consider it success
      }

      // Add the new space
      spaces.push({ key: spaceKey, name: spaceName });

      const storageKey = `confluence-spaces-project-${projectId}`;
      await kvs.set(storageKey, spaces);
      
      return true;
    } catch (error) {
      console.error(`Error adding configured space for project ${projectId}:`, error);
      return false;
    }
  }

  /**
   * Remove a space from the configured spaces list for a project
   * @param {string} projectId - Project ID
   * @param {string} spaceKey - Space key to remove
   * @returns {Promise<boolean>} Success status
   */
  async removeConfiguredSpace(projectId, spaceKey) {
    try {
      if (!projectId || typeof projectId !== 'string') {
        throw new Error('Project ID must be a non-empty string');
      }
      
      if (!spaceKey || typeof spaceKey !== 'string') {
        throw new Error('Space key must be a non-empty string');
      }

      const spaces = await this.getConfiguredSpaces(projectId);
      const filteredSpaces = spaces.filter(space => space.key !== spaceKey);

      const storageKey = `confluence-spaces-project-${projectId}`;
      await kvs.set(storageKey, filteredSpaces);
      
      return true;
    } catch (error) {
      console.error(`Error removing configured space for project ${projectId}:`, error);
      return false;
    }
  }

  /**
   * Get the configured Confluence pages list for a project
   * @param {string} projectId - Project ID
   * @returns {Promise<Array>} Array of page configs in standard format [{id, pageId, title, spaceKey, version}] or empty array
   */
  async getConfiguredPages(projectId) {
    try {
      const storageKey = `confluence-pages-project-${projectId}`;
      const pages = await kvs.get(storageKey);
      return pages || [];
    } catch (error) {
      console.error(`Error getting configured pages for project ${projectId}:`, error);
      return [];
    }
  }

  /**
   * Add a page to the configured pages list for a project
   * @param {string} projectId - Project ID
   * @param {string} pageId - Page ID to add
   * @param {string} pageTitle - Page title to add
   * @param {string} spaceKey - Space key the page belongs to
   * @param {number} version - Page version number (optional, defaults to 1)
   * @returns {Promise<boolean>} Success status
   */
  async addConfiguredPage(projectId, pageId, pageTitle, spaceKey, version = 1) {
    try {
      if (!projectId || typeof projectId !== 'string') {
        throw new Error('Project ID must be a non-empty string');
      }
      
      if (!pageId || typeof pageId !== 'string') {
        throw new Error('Page ID must be a non-empty string');
      }

      if (!pageTitle || typeof pageTitle !== 'string') {
        throw new Error('Page title must be a non-empty string');
      }

      if (!spaceKey || typeof spaceKey !== 'string') {
        throw new Error('Space key must be a non-empty string');
      }

      const pages = await this.getConfiguredPages(projectId);
      
      // Check if page already exists
      const existingPage = pages.find(page => page.pageId === pageId);
      if (existingPage) {
        return true; // Already exists, consider it success
      }

      // Add the new page in standard format compatible with Confluence API objects
      pages.push({ 
        id: pageId,
        pageId: pageId,
        title: pageTitle, 
        spaceKey,
        version: { number: version }
      });

      const storageKey = `confluence-pages-project-${projectId}`;
      await kvs.set(storageKey, pages);
      
      return true;
    } catch (error) {
      console.error(`Error adding configured page for project ${projectId}:`, error);
      return false;
    }
  }

  /**
   * Remove a page from the configured pages list for a project
   * @param {string} projectId - Project ID
   * @param {string} pageId - Page ID to remove
   * @returns {Promise<boolean>} Success status
   */
  async removeConfiguredPage(projectId, pageId) {
    try {
      if (!projectId || typeof projectId !== 'string') {
        throw new Error('Project ID must be a non-empty string');
      }
      
      if (!pageId || typeof pageId !== 'string') {
        throw new Error('Page ID must be a non-empty string');
      }

      const pages = await this.getConfiguredPages(projectId);
      const filteredPages = pages.filter(page => page.pageId !== pageId);

      const storageKey = `confluence-pages-project-${projectId}`;
      await kvs.set(storageKey, filteredPages);
      
      return true;
    } catch (error) {
      console.error(`Error removing configured page for project ${projectId}:`, error);
      return false;
    }
  }

  /**
   * Get ALL pages from configured spaces for a project with AI summaries for relevancy analysis
   * @param {string} projectId - Project ID
   * @returns {Promise<Array>} Array of all page objects with summaries (titles, metadata, and AI summaries)
   */
  async getAllPagesFromConfiguredSpaces(projectId) {
    try {
      const configuredSpaces = await this.getConfiguredSpaces(projectId);
      
      if (!configuredSpaces || configuredSpaces.length === 0) {
        console.log(`No configured spaces found for project ${projectId}`);
        return [];
      }
      
      const spaceKeys = configuredSpaces.map(space => space.key);
      const availablePages = await getAllPagesFromSpaces(spaceKeys);
      
      console.log(`Retrieved ${availablePages.length} pages from ${spaceKeys.length} configured spaces for project ${projectId} (with full pagination)`);
      
      if (availablePages.length === 0) {
        return [];
      }

      // Generate AI summaries for all pages (for relevancy analysis)
      console.log(`Gathering AI summaries for ${availablePages.length} pages for relevancy analysis...`);
      const pageSummaries = await this.getPageSummariesBatch(availablePages, projectId);
      
      // Add summaries to page objects
      const pagesWithSummaries = availablePages.map(page => ({
        ...page,
        summary: pageSummaries[page.pageId] || null
      }));
      
      console.log(`Gathered ${Object.keys(pageSummaries).length} summaries out of ${availablePages.length} pages for project ${projectId}`);
      return pagesWithSummaries;
    } catch (error) {
      console.error(`Error getting all pages from configured spaces for project ${projectId}:`, error);
      return [];
    }
  }

  /**
   * Clear all Confluence configuration (both spaces and pages lists) for a project
   * @param {string} projectId - Project ID
   * @returns {Promise<boolean>} Success status
   */
  async clearAllConfiguration(projectId) {
    try {
      if (!projectId || typeof projectId !== 'string') {
        throw new Error('Project ID must be a non-empty string');
      }

      // Clear both spaces and pages lists by setting them to empty arrays
      const spacesStorageKey = `confluence-spaces-project-${projectId}`;
      const pagesStorageKey = `confluence-pages-project-${projectId}`;
      
      const [spacesSuccess, pagesSuccess] = await Promise.all([
        kvs.set(spacesStorageKey, []).then(() => true).catch(() => false),
        kvs.set(pagesStorageKey, []).then(() => true).catch(() => false)
      ]);
      
      return spacesSuccess && pagesSuccess;
    } catch (error) {
      console.error(`Error clearing all Confluence configuration for project ${projectId}:`, error);
      return false;
    }
  }
}