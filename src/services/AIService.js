/**
 * AI Service - Handles AI-powered requirements expansion
 */

import { callOpenRouter } from '../utils/openrouter-client.js';
import { createFullPrompt, createPageRelevancyPrompt, createPageSummaryPrompt } from '../../build/system-prompt.js';
import { SettingsService } from './SettingsService.js';

export class AIService {
  constructor() {
    this.settingsService = new SettingsService();
  }
  
  /**
   * Analyze page relevancy to a Jira issue using AI with page summaries
   * @param {string} issueTitle - The Jira issue title/summary
   * @param {string} issuePrompt - The prompt/requirements text
   * @param {string} issueType - The issue type (story, task, bug, etc.)
   * @param {Array} availablePagesWithSummaries - Array of page objects with {title, pageId, spaceKey, summary}
   * @param {string} projectId - Project ID for AI config
   * @returns {Promise<Array>} Array of relevant page IDs
   */
  async analyzePageRelevancy(issueTitle, issuePrompt, issueType, availablePagesWithSummaries, projectId) {
    try {
      if (!availablePagesWithSummaries || availablePagesWithSummaries.length === 0) {
        console.log(`No pages provided for relevancy analysis`);
        return [];
      }

      const aiConfig = await this.settingsService.getAIConfig(projectId);
      
      // Create relevancy analysis prompt using built-in function with summaries
      const relevancyPrompt = createPageRelevancyPrompt(issueTitle, issuePrompt, issueType, availablePagesWithSummaries);
      console.log('Relevancy Analysis Prompt:\n', relevancyPrompt);
      
      const options = {
        model: aiConfig.model,
        temperature: 0.3, // Lower temperature for more focused relevancy analysis
        maxTokens: 400, // Shorter response needed for page ID list
        apiEndpoint: aiConfig.apiEndpoint
      };
      
      const result = await callOpenRouter(relevancyPrompt, options);
      
      if (!result) {
        console.warn('AI service returned null response for page relevancy analysis');
        return [];
      }

      // Parse the AI response to extract page IDs
      return this.parseRelevantPageIds(result, availablePagesWithSummaries);
    } catch (error) {
      console.error('Error in AI page relevancy analysis:', error);
      return []; // Return empty array on error rather than failing the whole process
    }
  }


  /**
   * Parse AI response to extract relevant page IDs
   * @param {string} aiResponse - The AI response containing page IDs
   * @param {Array} availablePages - Available pages for validation
   * @returns {Array} Array of valid page IDs
   */
  parseRelevantPageIds(aiResponse, availablePages) {
    try {
      // Clean the response to extract just the array part
      const cleanResponse = aiResponse.trim();
      let jsonMatch = cleanResponse.match(/\[[\d,\s]*\]/);
      
      if (!jsonMatch) {
        // Try to find numbers in the response as fallback
        const numbers = cleanResponse.match(/\d+/g);
        if (numbers) {
          jsonMatch = [`[${numbers.join(',')}]`];
        } else {
          console.warn('Could not parse page IDs from AI response:', cleanResponse);
          return [];
        }
      }

      const pageIds = JSON.parse(jsonMatch[0]);
      const availablePageIds = availablePages.map(p => p.pageId);
      
      // Filter to only include valid page IDs and convert to strings for consistency
      const validPageIds = pageIds
        .map(id => String(id))
        .filter(id => availablePageIds.includes(id));

      console.log(`AI identified ${validPageIds.length} relevant pages out of ${availablePages.length} available`);
      return validPageIds;
    } catch (error) {
      console.error('Error parsing AI relevancy response:', error, 'Response:', aiResponse);
      return [];
    }
  }
  
  /**
   * Generate AI summary for a Confluence page
   * @param {string} pageTitle - The Confluence page title
   * @param {string} pageContent - The page content body
   * @param {string} projectId - Project ID for AI config
   * @returns {Promise<string|null>} Generated summary or null if failed
   */
  async generatePageSummary(pageTitle, pageContent, projectId) {
    try {
      if (!pageTitle || !pageContent || !projectId) {
        throw new Error('Page title, content, and project ID are required for summary generation');
      }

      const aiConfig = await this.settingsService.getAIConfig(projectId);
      
      // Create summary prompt using built-in function
      const summaryPrompt = createPageSummaryPrompt(pageTitle, pageContent);
      
      const options = {
        model: aiConfig.model,
        temperature: 0.3, // Lower temperature for more focused summaries
        maxTokens: 200, // Limit to 200 tokens for concise summaries
        apiEndpoint: aiConfig.apiEndpoint
      };
      
      const result = await callOpenRouter(summaryPrompt, options);
      
      if (!result) {
        console.warn('AI service returned null response for page summary generation');
        return null;
      }

      // Clean up the summary (remove any extra formatting)
      const cleanSummary = result.trim();
      console.log(`Generated summary for page "${pageTitle}" (${cleanSummary.length} characters)`);
      
      return cleanSummary;
    } catch (error) {
      console.error('Error in AI page summary generation:', error);
      return null; // Return null on error rather than failing the whole process
    }
  }

  /**
   * Expand simple requirements using AI
   * @param {string} issueTitle - The Jira issue title/summary
   * @param {string} simpleRequirements - The prompt text
   * @param {string} issueType - The issue type (story, task, bug) - optional for backward compatibility
   * @param {string} projectId - Project ID for project-scoped settings
   * @param {Object} options - Optional configuration overrides, can include { projectContext }
   * @returns {Promise<string|null>} Expanded requirements or null if failed
   */
  async expandRequirements(issueTitle, simpleRequirements, issueType = null, projectId = null, options = {}) {
    try {
      if (!projectId) {
        throw new Error('Project ID is required for AI expansion');
      }
      
      const customTemplate = issueType 
        ? await this.settingsService.getTemplateForIssueType(issueType, projectId)
        : await this.settingsService.getTemplateForIssueType('story', projectId);
      
      const aiConfig = await this.settingsService.getAIConfig(projectId);
      
      // Use provided project context
      const projectContext = options.projectContext || '';
      console.log('Using pre-analyzed project context');
      
      const finalOptions = {
        model: aiConfig.model,
        temperature: aiConfig.temperature,
        maxTokens: aiConfig.maxTokens,
        apiEndpoint: aiConfig.apiEndpoint,
        ...options
      };
      
      const prompt = createFullPrompt(issueTitle, simpleRequirements, customTemplate, projectContext);
      
      const result = await callOpenRouter(prompt, finalOptions);
      
      if (!result) {
        console.error('AI service returned null response');
        return null;
      }

      return result;
    } catch (error) {
      console.error('Error in AI service expansion:', error);
      throw error;
    }
  }
}