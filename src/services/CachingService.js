/**
 * Caching Service - Handles version-based caching for Confluence content
 */

import { kvs } from '@forge/kvs';

export class CachingService {
  constructor() {
    // Cache key patterns
    this.patterns = {
      content: 'cache-content-project-{projectId}-page-{pageId}',
      summary: 'cache-summary-project-{projectId}-page-{pageId}'
    };
  }

  /**
   * Generate cache key from pattern
   * @param {string} pattern - Cache key pattern with placeholders
   * @param {Object} params - Parameters to replace in pattern
   * @returns {string} Generated cache key
   */
  generateCacheKey(pattern, params) {
    let key = pattern;
    for (const [param, value] of Object.entries(params)) {
      key = key.replace(`{${param}}`, value);
    }
    return key;
  }

  /**
   * Check if cached data is still valid based on version
   * @param {Object} cachedData - Cached data object
   * @param {number|string} currentVersion - Current version to compare against
   * @returns {boolean} True if cache is valid
   */
  isCacheValid(cachedData, currentVersion) {
    if (!cachedData || !cachedData.version || currentVersion === undefined) {
      return false;
    }
    
    // Convert both to strings for consistent comparison
    const cachedVersion = String(cachedData.version);
    const currentVersionStr = String(currentVersion);
    
    return cachedVersion === currentVersionStr;
  }

  /**
   * Get cached page content
   * @param {string} pageId - Page ID
   * @param {string} projectId - Project ID for cache scoping
   * @param {number|string} currentVersion - Current page version
   * @returns {Promise<Object|null>} Cached page data or null if invalid/missing
   */
  async getCachedPageContent(pageId, projectId, currentVersion) {
    try {
      const cacheKey = this.generateCacheKey(this.patterns.content, { projectId, pageId });
      const cachedData = await kvs.get(cacheKey);
      
      if (this.isCacheValid(cachedData, currentVersion)) {
        console.log(`Cache hit for page ${pageId} with version ${currentVersion}`);
        return cachedData.data;
      }
      
      console.log(`Cache miss for page ${pageId} (cached: ${cachedData?.version || 'none'}, current: ${currentVersion})`);
      return null;
    } catch (error) {
      console.error(`Error getting cached page content for ${pageId}:`, error);
      return null;
    }
  }

  /**
   * Set cached page content
   * @param {string} pageId - Page ID
   * @param {string} projectId - Project ID for cache scoping
   * @param {Object} pageData - Page data to cache
   * @returns {Promise<boolean>} Success status
   */
  async setCachedPageContent(pageId, projectId, pageData) {
    try {
      if (!pageData || !pageId || !projectId) {
        throw new Error('Invalid parameters for caching page content');
      }

      const cacheKey = this.generateCacheKey(this.patterns.content, { projectId, pageId });
      const cacheData = {
        data: pageData,
        version: pageData.version?.number || 1,
        cached: Date.now()
      };
      
      await kvs.set(cacheKey, cacheData);
      console.log(`Cached page ${pageId} with version ${cacheData.version}`);
      return true;
    } catch (error) {
      console.error(`Error caching page content for ${pageId}:`, error);
      return false;
    }
  }

  /**
   * Get cached page summary
   * @param {string} pageId - Page ID
   * @param {string} projectId - Project ID for cache scoping
   * @param {number|string} currentVersion - Current page version
   * @returns {Promise<string|null>} Cached summary or null if invalid/missing
   */
  async getCachedPageSummary(pageId, projectId, currentVersion) {
    try {
      const cacheKey = this.generateCacheKey(this.patterns.summary, { projectId, pageId });
      const cachedData = await kvs.get(cacheKey);
      
      if (this.isCacheValid(cachedData, currentVersion)) {
        console.log(`Summary cache hit for page ${pageId} with version ${currentVersion}`);
        return cachedData.summary;
      }
      
      console.log(`Summary cache miss for page ${pageId} (cached: ${cachedData?.version || 'none'}, current: ${currentVersion})`);
      return null;
    } catch (error) {
      console.error(`Error getting cached page summary for ${pageId}:`, error);
      return null;
    }
  }

  /**
   * Set cached page summary
   * @param {string} pageId - Page ID
   * @param {string} projectId - Project ID for cache scoping
   * @param {string} summary - AI-generated summary
   * @param {number|string} pageVersion - Page version for cache validation
   * @returns {Promise<boolean>} Success status
   */
  async setCachedPageSummary(pageId, projectId, summary, pageVersion) {
    try {
      if (!summary || !pageId || !projectId || pageVersion === undefined) {
        throw new Error('Invalid parameters for caching page summary');
      }

      const cacheKey = this.generateCacheKey(this.patterns.summary, { projectId, pageId });
      const cacheData = {
        summary: summary,
        version: pageVersion,
        cached: Date.now()
      };
      
      await kvs.set(cacheKey, cacheData);
      console.log(`Cached summary for page ${pageId} with version ${pageVersion}`);
      return true;
    } catch (error) {
      console.error(`Error caching page summary for ${pageId}:`, error);
      return false;
    }
  }

  /**
   * Get multiple cached page contents
   * @param {Array} pageIds - Array of page IDs
   * @param {string} projectId - Project ID for cache scoping
   * @param {Object} versionMap - Map of pageId -> version
   * @returns {Promise<Object>} Object with cached pages and uncached page IDs
   */
  async getCachedPageContents(pageIds, projectId, versionMap) {
    const cached = [];
    const uncachedIds = [];
    
    await Promise.all(
      pageIds.map(async (pageId) => {
        const currentVersion = versionMap[pageId];
        const cachedData = await this.getCachedPageContent(pageId, projectId, currentVersion);
        
        if (cachedData) {
          cached.push(cachedData);
        } else {
          uncachedIds.push(pageId);
        }
      })
    );
    
    return { cached, uncachedIds };
  }

  /**
   * Get multiple cached page summaries
   * @param {Array} pageIds - Array of page IDs
   * @param {string} projectId - Project ID for cache scoping
   * @param {Object} versionMap - Map of pageId -> version
   * @returns {Promise<Object>} Object with cached summaries and uncached page IDs
   */
  async getCachedPageSummaries(pageIds, projectId, versionMap) {
    const cached = {};
    const uncachedIds = [];
    
    await Promise.all(
      pageIds.map(async (pageId) => {
        const currentVersion = versionMap[pageId];
        const cachedSummary = await this.getCachedPageSummary(pageId, projectId, currentVersion);
        
        if (cachedSummary) {
          cached[pageId] = cachedSummary;
        } else {
          uncachedIds.push(pageId);
        }
      })
    );
    
    return { cached, uncachedIds };
  }

  /**
   * Invalidate cache entries for a page (both content and summary)
   * @param {string} pageId - Page ID
   * @param {string} projectId - Project ID for cache scoping
   * @returns {Promise<boolean>} Success status
   */
  async invalidatePageCache(pageId, projectId) {
    try {
      const contentKey = this.generateCacheKey(this.patterns.content, { projectId, pageId });
      const summaryKey = this.generateCacheKey(this.patterns.summary, { projectId, pageId });
      
      await Promise.all([
        kvs.delete(contentKey),
        kvs.delete(summaryKey)
      ]);
      
      console.log(`Invalidated cache for page ${pageId}`);
      return true;
    } catch (error) {
      console.error(`Error invalidating cache for page ${pageId}:`, error);
      return false;
    }
  }
}