/**
 * License Error Utilities
 * 
 * Provides utilities for creating consistent license-related error messages
 * and ADF content for display in Jira issues.
 */

/**
 * Create ADF content for license-related messages
 * @param {string} message - The message to display
 * @param {string} type - Message type: 'warning', 'error', 'info'
 * @param {string} upgradeUrl - Optional upgrade URL
 * @returns {Object} ADF content object
 */
export function createLicenseMessageADF(message, type = 'warning', upgradeUrl = null) {
  const panelType = type === 'error' ? 'error' : type === 'info' ? 'info' : 'warning';
  
  const content = [{
    type: 'paragraph',
    content: [{
      type: 'text',
      text: message
    }]
  }];

  // Add upgrade link if provided
  if (upgradeUrl) {
    content.push({
      type: 'paragraph',
      content: [{
        type: 'text',
        text: 'Upgrade your license: '
      }, {
        type: 'text',
        text: 'Get License',
        marks: [{
          type: 'link',
          attrs: {
            href: upgradeUrl
          }
        }]
      }]
    });
  }

  return {
    version: 1,
    type: 'doc',
    content: [{
      type: 'panel',
      attrs: { panelType },
      content
    }]
  };
}

/**
 * Get standard license error messages
 */
export const LICENSE_MESSAGES = {
  UNLICENSED: 'This feature requires a valid license. Please contact your administrator to upgrade your license.',
  FEATURE_NOT_AVAILABLE: 'This feature is not available in your current license tier. Please upgrade to access this functionality.',
  VALIDATION_FAILED: 'Unable to verify license status. Please try again later or contact support if the issue persists.',
  GRACE_PERIOD: 'License validation is temporarily unavailable. Full functionality will continue for a limited time.',
  EXPIRED: 'Your license has expired. Please renew your license to continue using this feature.',
  AI_EXPANSION_BLOCKED: 'AI Requirements Expansion is not available with your current license. Please upgrade to use this feature.',
  CONFLUENCE_INTEGRATION_BLOCKED: 'Confluence Integration is not available with your current license. Please upgrade to use this feature.',
  ADVANCED_TEMPLATES_BLOCKED: 'Advanced Templates are not available with your current license. Please upgrade to use this feature.'
};

/**
 * Create a license enforcement result for blocking operations
 * @param {string} reason - The reason for blocking
 * @param {string} message - Custom message (optional)
 * @param {string} upgradeUrl - Upgrade URL (optional)
 * @returns {Object} Enforcement result
 */
export function createLicenseBlock(reason, message = null, upgradeUrl = null) {
  const defaultMessages = {
    'unlicensed': LICENSE_MESSAGES.UNLICENSED,
    'feature_not_available': LICENSE_MESSAGES.FEATURE_NOT_AVAILABLE,
    'validation_failed': LICENSE_MESSAGES.VALIDATION_FAILED,
    'expired': LICENSE_MESSAGES.EXPIRED
  };

  return {
    allowed: false,
    reason,
    message: message || defaultMessages[reason] || 'License validation failed',
    upgradeUrl: upgradeUrl || 'https://marketplace.atlassian.com/apps/your-app-key'
  };
}

/**
 * Create a license enforcement result for allowing operations
 * @param {string} message - Success message (optional)
 * @returns {Object} Enforcement result
 */
export function createLicenseAllow(message = 'Operation allowed') {
  return {
    allowed: true,
    reason: 'licensed',
    message
  };
}

/**
 * Get user-friendly feature names
 */
export const FEATURE_NAMES = {
  aiRequirementsExpansion: 'AI Requirements Expansion',
  confluenceIntegration: 'Confluence Integration',
  advancedTemplates: 'Advanced Templates',
  bulkProcessing: 'Bulk Processing',
  prioritySupport: 'Priority Support'
};

/**
 * Get feature-specific error messages
 * @param {string} featureName - The feature name
 * @returns {string} Feature-specific error message
 */
export function getFeatureBlockedMessage(featureName) {
  const friendlyName = FEATURE_NAMES[featureName] || featureName;
  return `${friendlyName} is not available with your current license. Please upgrade to access this functionality.`;
}

/**
 * Create ADF content for feature-specific license blocks
 * @param {string} featureName - The feature name
 * @param {string} upgradeUrl - Optional upgrade URL
 * @returns {Object} ADF content object
 */
export function createFeatureBlockedADF(featureName, upgradeUrl = null) {
  const message = getFeatureBlockedMessage(featureName);
  return createLicenseMessageADF(message, 'warning', upgradeUrl);
}

/**
 * Log license enforcement actions for monitoring
 * @param {string} operation - The operation being performed
 * @param {string} issueKey - The Jira issue key (if applicable)
 * @param {Object} enforcement - The enforcement result
 * @param {Object} licenseStatus - The license status
 */
export function logLicenseEnforcement(operation, issueKey, enforcement, licenseStatus) {
  const logData = {
    operation,
    issueKey,
    allowed: enforcement.allowed,
    reason: enforcement.reason,
    licenseStatus: licenseStatus.status,
    tier: licenseStatus.tier,
    timestamp: new Date().toISOString()
  };

  if (enforcement.allowed) {
    console.log(`License enforcement: ALLOWED - ${operation}`, logData);
  } else {
    console.warn(`License enforcement: BLOCKED - ${operation}`, logData);
  }
}
