import { RequirementsService, JiraService, QueueService, AIService, ConfluenceService, LicenseService } from '../services/index.js';
import { config } from '../config/index.js';

export const promptUpdateHandler = async (event, context) => {
  const issueId = event.issue.id;
  const issueKey = event.issue.key;
  const isCreation = !event.changelog || !event.changelog.items || event.changelog.items.length === 0;

  console.log(`Processing ${isCreation ? 'creation' : 'update'} for issue ${issueKey}`);

  const jiraService = new JiraService();
  const queueService = new QueueService();
  const licenseService = new LicenseService();

  // Validate license before processing
  try {
    const cloudId = context?.cloudId || 'unknown';
    const licenseStatus = await licenseService.validateLicense(cloudId);

    // Check if AI requirements expansion is allowed
    const enforcement = licenseService.enforceLicense(licenseStatus, 'promptUpdate', 'aiRequirementsExpansion', issueKey);

    if (!enforcement.allowed) {
      console.warn(`License enforcement blocked prompt update for ${issueKey}: ${enforcement.reason}`);
      // Simply block the operation - no messages in customer issues
      return;
    }

    console.log(`License validation passed for ${issueKey}: ${licenseStatus.status} (${licenseStatus.tier})`);
  } catch (error) {
    console.error(`License validation failed for ${issueKey}:`, error);
    // Continue with processing during license validation errors (grace period handling)
  }

  const issue = await jiraService.getIssue(issueId);
  if (!issue || !issue.fields) {
    console.error(`Could not fetch issue details for ${issueKey}`);
    return;
  }

  const projectId = issue.fields.project.id;
  const issueTypeId = issue.fields.issuetype.id;
  const issueTypeName = issue.fields.issuetype.name;

  const [simpleReqField, summaryField] = await jiraService.getFieldsByName([
    config.fields.prompt,
    config.fields.summary
  ], projectId, issueTypeId);

  // Resolve output field with fallback logic
  const fullReqField = await jiraService.resolveOutputFieldForIssueType(issueTypeName, projectId, issueTypeId);

  if (!simpleReqField || !fullReqField || !summaryField) {
    console.error('Could not resolve required fields');
    return;
  }

  if (!isCreation && !jiraService.isFieldUpdated(event.changelog, simpleReqField)) {
    console.log(`${config.fields.prompt} field was not updated for ${issueKey}, skipping`);
    return;
  }

  const issueSimpleRequirements = jiraService.extractFieldValueFromIssue(issue, simpleReqField);
  if (!issueSimpleRequirements) {
    return;
  }

  const issueTitle = jiraService.extractFieldValueFromIssue(issue, summaryField) || '';

  try {
    // Set loading message before processing
    const requirementsService = new RequirementsService();
    const loadingMessage = requirementsService.createLoadingMessage();
    await jiraService.updateIssueFields(issueId, {
      [fullReqField.id]: loadingMessage
    });

    const jobId = await queueService.enqueueRequirementsProcessing({
      issue,
      simpleRequirements: issueSimpleRequirements,
      fullReqFieldId: fullReqField.id,
      fullReqFieldName: fullReqField.name
    });
    console.log(`Enqueued Requirements processing for ${issueKey}`);
  } catch (error) {
    console.error(`Failed to enqueue processing for ${issueKey}:`, error);
  }
};

export const requirementsProcessorHandler = async (event) => {
  const aiService = new AIService();
  const requirementsService = new RequirementsService();
  const jiraService = new JiraService();
  const confluenceService = new ConfluenceService();
  const licenseService = new LicenseService();

  const { issue, simpleRequirements, fullReqFieldId, fullReqFieldName } = event.call.payload.body;
  const issueId = issue.id;
  const issueKey = issue.key;
  const issueTitle = issue.fields.summary;
  const issueType = issue.fields.issuetype?.name?.toLowerCase() || 'story';
  const projectId = issue.fields.project?.id;

  console.log(`Processing AI expansion for ${issueKey} (${issueType})`);

  // Validate license before AI processing
  try {
    const cloudId = event.context?.cloudId || 'unknown';
    const licenseStatus = await licenseService.validateLicense(cloudId);

    // Check if AI requirements expansion is allowed
    const enforcement = licenseService.enforceLicense(licenseStatus, 'aiExpansion', 'aiRequirementsExpansion', issueKey);

    if (!enforcement.allowed) {
      console.warn(`License enforcement blocked AI expansion for ${issueKey}: ${enforcement.reason}`);
      // Simply block the operation - no messages in customer issues
      return;
    }

    console.log(`License validation passed for AI expansion ${issueKey}: ${licenseStatus.status} (${licenseStatus.tier})`);
  } catch (error) {
    console.error(`License validation failed for AI expansion ${issueKey}:`, error);
    // Continue with processing during license validation errors (grace period handling)
  }

  try {
    // Step 1: Get ALL pages from configured spaces for AI analysis (with full pagination)
    console.log(`[${issueKey}] Step 1: Fetching ALL pages from configured spaces...`);
    const availablePages = await confluenceService.getAllPagesFromConfiguredSpaces(projectId);
    console.log(`[${issueKey}] Step 1 Complete: Retrieved ${availablePages.length} pages from configured spaces`);
    
    // Step 2: Analyze page relevancy with AI
    console.log(`[${issueKey}] Step 2: Analyzing page relevancy with AI for ${availablePages.length} pages...`);
    const relevantPageIds = await aiService.analyzePageRelevancy(
      issueTitle, 
      simpleRequirements, 
      issueType, 
      availablePages,
      projectId
    );
    console.log(`[${issueKey}] Step 2 Complete: AI identified ${relevantPageIds.length} relevant pages out of ${availablePages.length} available`);

    // Step 3: Get project context using the AI-recommended pages (with metadata)
    const relevantPages = availablePages.filter(page => relevantPageIds.includes(page.pageId || page.id));
    console.log(`[${issueKey}] Step 3: Building project context from ${relevantPages.length} AI-recommended + Project Context pages...`);
    const projectContext = await confluenceService.getProjectContextWithRelevantPages(
      projectId, 
      relevantPages
    );
    const contextLength = projectContext.length;
    const contextWords = contextLength > 0 ? projectContext.split(/\s+/).length : 0;
    console.log(`[${issueKey}] Step 3 Complete: Built project context with ${contextLength} characters (${contextWords} words)`);
    
    // Step 4: Expand requirements using the targeted project context
    console.log(`[${issueKey}] Step 4: Expanding requirements using AI with ${contextWords} words of context...`);
    const rawRequirements = await aiService.expandRequirements(
      issueTitle, 
      simpleRequirements, 
      issueType, 
      projectId, 
      { projectContext }
    );
    
    if (!rawRequirements) {
      throw new Error('AI service failed to expand requirements');
    }

    const adfContent = requirementsService.processRequirements(rawRequirements);

    const updateSuccess = await jiraService.updateIssueFields(issueId, {
      [fullReqFieldId]: adfContent
    });

    if (!updateSuccess) {
      throw new Error('Failed to update Jira issue with expanded requirements');
    }

    console.log(`Updated "${fullReqFieldName || 'output'}" field for ${issueKey}`);
  } catch (error) {
    console.error(`Error processing requirements for ${issueKey}:`, error);
    throw error;
  }
};
